<?php

/**
 * Get all recommended products with their product details
 * @param bool $active_only Whether to only return active products
 * @return array Array of recommended products with product details
 */
function get_recommended_products(bool $active_only = true): array
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return [];
    }

    try {
        $sql = "SELECT rp.*, p.name_pt, p.slug, p.base_price, p.is_active, p.product_type,
                       pi.filename as image_filename
                FROM recommended_products rp
                INNER JOIN products p ON rp.product_id = p.id
                LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_default = 1
                " . ($active_only ? "WHERE p.is_active = 1" : "") . "
                ORDER BY rp.display_order ASC, rp.created_at ASC";

        $stmt = $pdo->prepare($sql);
        $stmt->execute();
        $results = $stmt->fetchAll();

        // Add image URLs to results
        foreach ($results as &$product) {
            if (!empty($product['image_filename'])) {
                $base_url = defined('BASE_URL') ? BASE_URL : '';
                $product['display_image_url'] = $base_url . '/public/assets/images/products/' . $product['image_filename'];
            } else {
                $product['display_image_url'] = null;
            }
        }

        return $results;
    } catch (PDOException $e) {
        error_log("Error getting recommended products: " . $e->getMessage());
        return [];
    }
}

/**
 * Get recommended products in random order for frontend display
 * @param int $limit Maximum number of products to return
 * @return array Array of recommended products in random order
 */
function get_recommended_products_random(int $limit = 0): array
{
    $products = get_recommended_products(true);
    
    // Shuffle the array to randomize order
    shuffle($products);
    
    // Apply limit if specified
    if ($limit > 0) {
        $products = array_slice($products, 0, $limit);
    }
    
    return $products;
}

/**
 * Add a product to recommended products
 * @param int $product_id The product ID to add
 * @param int $display_order Optional display order (0 = auto)
 * @return bool True on success, false on failure
 */
function add_recommended_product(int $product_id, int $display_order = 0): bool
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        // Check if product exists and is active
        $stmt = $pdo->prepare("SELECT id FROM products WHERE id = :product_id AND is_active = 1");
        $stmt->execute([':product_id' => $product_id]);
        if (!$stmt->fetch()) {
            return false;
        }

        // If display_order is 0, get the next available order
        if ($display_order === 0) {
            $stmt = $pdo->query("SELECT COALESCE(MAX(display_order), 0) + 1 as next_order FROM recommended_products");
            $result = $stmt->fetch();
            $display_order = $result['next_order'] ?? 1;
        }

        // Insert the recommended product
        $stmt = $pdo->prepare("INSERT INTO recommended_products (product_id, display_order) VALUES (:product_id, :display_order)");
        return $stmt->execute([
            ':product_id' => $product_id,
            ':display_order' => $display_order
        ]);
    } catch (PDOException $e) {
        error_log("Error adding recommended product: " . $e->getMessage());
        return false;
    }
}

/**
 * Remove a product from recommended products
 * @param int $product_id The product ID to remove
 * @return bool True on success, false on failure
 */
function remove_recommended_product(int $product_id): bool
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        $stmt = $pdo->prepare("DELETE FROM recommended_products WHERE product_id = :product_id");
        return $stmt->execute([':product_id' => $product_id]);
    } catch (PDOException $e) {
        error_log("Error removing recommended product: " . $e->getMessage());
        return false;
    }
}

/**
 * Check if a product is in the recommended products list
 * @param int $product_id The product ID to check
 * @return bool True if product is recommended, false otherwise
 */
function is_product_recommended(int $product_id): bool
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        $stmt = $pdo->prepare("SELECT id FROM recommended_products WHERE product_id = :product_id");
        $stmt->execute([':product_id' => $product_id]);
        return (bool) $stmt->fetch();
    } catch (PDOException $e) {
        error_log("Error checking if product is recommended: " . $e->getMessage());
        return false;
    }
}

/**
 * Update the display order of recommended products
 * @param array $order_data Array of ['product_id' => order] pairs
 * @return bool True on success, false on failure
 */
function update_recommended_products_order(array $order_data): bool
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return false;
    }

    try {
        $pdo->beginTransaction();

        foreach ($order_data as $product_id => $order) {
            $stmt = $pdo->prepare("UPDATE recommended_products SET display_order = :order, updated_at = datetime('now', 'localtime') WHERE product_id = :product_id");
            $stmt->execute([
                ':order' => $order,
                ':product_id' => $product_id
            ]);
        }

        $pdo->commit();
        return true;
    } catch (PDOException $e) {
        $pdo->rollBack();
        error_log("Error updating recommended products order: " . $e->getMessage());
        return false;
    }
}

/**
 * Get count of recommended products
 * @return int Number of recommended products
 */
function get_recommended_products_count(): int
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return 0;
    }

    try {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM recommended_products rp INNER JOIN products p ON rp.product_id = p.id WHERE p.is_active = 1");
        $result = $stmt->fetch();
        return (int) ($result['count'] ?? 0);
    } catch (PDOException $e) {
        error_log("Error getting recommended products count: " . $e->getMessage());
        return 0;
    }
}

/**
 * Get all active products that are not yet recommended (for admin selection)
 * @param string $search_term Optional search term to filter products
 * @param int $limit Maximum number of products to return
 * @return array Array of products available for recommendation
 */
function get_products_available_for_recommendation(string $search_term = '', int $limit = 50): array
{
    $pdo = get_db_connection();
    if (!$pdo) {
        return [];
    }

    try {
        $sql = "SELECT p.id, p.name_pt, p.slug, p.base_price, p.product_type, p.sku,
                       pi.filename as image_filename
                FROM products p
                LEFT JOIN product_images pi ON p.id = pi.product_id AND pi.is_default = 1
                WHERE p.is_active = 1 
                AND p.id NOT IN (SELECT product_id FROM recommended_products)";

        $params = [];
        if (!empty($search_term)) {
            $sql .= " AND (p.name_pt LIKE :search OR p.sku LIKE :search)";
            $params[':search'] = '%' . $search_term . '%';
        }

        $sql .= " ORDER BY p.name_pt ASC";
        
        if ($limit > 0) {
            $sql .= " LIMIT :limit";
            $params[':limit'] = $limit;
        }

        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $results = $stmt->fetchAll();

        // Add image URLs to results
        foreach ($results as &$product) {
            if (!empty($product['image_filename'])) {
                $base_url = defined('BASE_URL') ? BASE_URL : '';
                $product['display_image_url'] = $base_url . '/public/assets/images/products/' . $product['image_filename'];
            } else {
                $product['display_image_url'] = null;
            }
        }

        return $results;
    } catch (PDOException $e) {
        error_log("Error getting products available for recommendation: " . $e->getMessage());
        return [];
    }
}
