<?php

function create_recommended_products_table(PDO $pdo): void
{
    try {
        // Check if the table already exists
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='recommended_products';");
        $table_exists = $stmt->fetch();

        if (!$table_exists) {
            // Create the recommended_products table
            $pdo->exec("CREATE TABLE recommended_products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                display_order INTEGER NOT NULL DEFAULT 0,
                created_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                updated_at TEXT NOT NULL DEFAULT (datetime('now', 'localtime')),
                FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
                UNIQUE (product_id)
            );");

            // Create index for better performance
            $pdo->exec("CREATE INDEX idx_recommended_products_product_id ON recommended_products (product_id);");
            $pdo->exec("CREATE INDEX idx_recommended_products_display_order ON recommended_products (display_order);");
        }
    } catch (PDOException $e) {
        // Log error but don't break the application
        error_log("Error creating recommended_products table: " . $e->getMessage());
    }
}
