<?php
// Handle AJAX actions
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $action = $_POST['action'];
    $response = ['success' => false, 'message' => ''];
    
    switch ($action) {
        case 'add_recommended':
            $product_id = (int) ($_POST['product_id'] ?? 0);
            if ($product_id > 0) {
                if (add_recommended_product($product_id)) {
                    $response['success'] = true;
                    $response['message'] = 'Produto adicionado aos recomendados com sucesso!';
                } else {
                    $response['message'] = 'Erro ao adicionar produto aos recomendados.';
                }
            } else {
                $response['message'] = 'ID do produto inválido.';
            }
            break;
            
        case 'remove_recommended':
            $product_id = (int) ($_POST['product_id'] ?? 0);
            if ($product_id > 0) {
                if (remove_recommended_product($product_id)) {
                    $response['success'] = true;
                    $response['message'] = 'Produto removido dos recomendados com sucesso!';
                } else {
                    $response['message'] = 'Erro ao remover produto dos recomendados.';
                }
            } else {
                $response['message'] = 'ID do produto inválido.';
            }
            break;
            
        case 'update_order':
            $order_data = $_POST['order_data'] ?? [];
            if (!empty($order_data) && is_array($order_data)) {
                if (update_recommended_products_order($order_data)) {
                    $response['success'] = true;
                    $response['message'] = 'Ordem dos produtos atualizada com sucesso!';
                } else {
                    $response['message'] = 'Erro ao atualizar ordem dos produtos.';
                }
            } else {
                $response['message'] = 'Dados de ordenação inválidos.';
            }
            break;
    }
    
    // Return JSON response for AJAX requests
    if (isset($_GET['ajax']) || (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest')) {
        header('Content-Type: application/json');
        echo json_encode($response);
        exit;
    }
    
    // For non-AJAX requests, add flash message and redirect
    if ($response['success']) {
        add_flash_message($response['message'], 'success');
    } else {
        add_flash_message($response['message'], 'danger');
    }
    
    header('Location: admin.php?section=recommended_products&' . get_session_id_param());
    exit;
}

// Get data for display
$recommended_products = $recommended_products ?? get_recommended_products(true);
$recommended_count = $recommended_count ?? get_recommended_products_count();
$search_term = $search_term ?? ($_GET['search'] ?? '');
$available_products = $available_products ?? get_products_available_for_recommendation($search_term, 20);
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">Produtos Recomendados</h1>
                <div class="text-muted">
                    <i class="bi bi-star"></i> <?= $recommended_count ?> produto<?= $recommended_count !== 1 ? 's' : '' ?> recomendado<?= $recommended_count !== 1 ? 's' : '' ?>
                </div>
            </div>

            <?php if (isset($_SESSION['flash_messages'])): ?>
                <?php foreach ($_SESSION['flash_messages'] as $message): ?>
                    <div class="alert alert-<?= htmlspecialchars($message['type']) ?> alert-dismissible fade show" role="alert">
                        <?= htmlspecialchars($message['message']) ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                <?php endforeach; ?>
                <?php unset($_SESSION['flash_messages']); ?>
            <?php endif; ?>

            <!-- Current Recommended Products -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-star-fill text-warning"></i> Produtos Recomendados Atuais
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($recommended_products)): ?>
                        <div class="text-center py-4">
                            <i class="bi bi-star display-4 text-muted"></i>
                            <p class="text-muted mt-2">Nenhum produto recomendado ainda.</p>
                            <p class="text-muted">Use a seção abaixo para adicionar produtos à lista de recomendados.</p>
                        </div>
                    <?php else: ?>
                        <div class="row" id="recommended-products-list">
                            <?php foreach ($recommended_products as $product): ?>
                                <div class="col-md-6 col-lg-4 mb-3" data-product-id="<?= $product['product_id'] ?>">
                                    <div class="card h-100">
                                        <?php if ($product['display_image_url']): ?>
                                            <img src="<?= htmlspecialchars($product['display_image_url']) ?>" 
                                                 class="card-img-top" 
                                                 style="height: 200px; object-fit: cover;" 
                                                 alt="<?= htmlspecialchars($product['name_pt']) ?>">
                                        <?php else: ?>
                                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 200px;">
                                                <i class="bi bi-image text-muted" style="font-size: 3rem;"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div class="card-body d-flex flex-column">
                                            <h6 class="card-title"><?= htmlspecialchars($product['name_pt']) ?></h6>
                                            <p class="card-text text-muted small">
                                                Tipo: <?= ucfirst($product['product_type']) ?><br>
                                                Preço: €<?= number_format($product['base_price'], 2, ',', '.') ?>
                                            </p>
                                            <div class="mt-auto">
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-muted">Ordem: <?= $product['display_order'] ?></small>
                                                    <div class="btn-group btn-group-sm">
                                                        <a href="<?= BASE_URL ?>/<?= $product['slug'] ?>" 
                                                           class="btn btn-outline-primary btn-sm" 
                                                           target="_blank" 
                                                           title="Ver produto">
                                                            <i class="bi bi-eye"></i>
                                                        </a>
                                                        <button type="button" 
                                                                class="btn btn-outline-danger btn-sm remove-recommended-btn" 
                                                                data-product-id="<?= $product['product_id'] ?>"
                                                                title="Remover dos recomendados">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <i class="bi bi-info-circle"></i>
                            <strong>Nota:</strong> Os produtos recomendados aparecerão na página inicial em ordem aleatória a cada carregamento.
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Add New Recommended Products -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-plus-circle"></i> Adicionar Produtos Recomendados
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Search Form -->
                    <form method="GET" class="mb-4">
                        <input type="hidden" name="section" value="recommended_products">
                        <input type="hidden" name="<?= get_session_id_param_name() ?>" value="<?= get_session_id() ?>">
                        <div class="input-group">
                            <input type="text" 
                                   class="form-control" 
                                   name="search" 
                                   value="<?= htmlspecialchars($search_term) ?>" 
                                   placeholder="Pesquisar produtos por nome ou SKU...">
                            <button class="btn btn-outline-secondary" type="submit">
                                <i class="bi bi-search"></i> Pesquisar
                            </button>
                            <?php if (!empty($search_term)): ?>
                                <a href="admin.php?section=recommended_products&<?= get_session_id_param() ?>" 
                                   class="btn btn-outline-secondary">
                                    <i class="bi bi-x"></i> Limpar
                                </a>
                            <?php endif; ?>
                        </div>
                    </form>

                    <!-- Available Products -->
                    <?php if (empty($available_products)): ?>
                        <div class="text-center py-4">
                            <i class="bi bi-box display-4 text-muted"></i>
                            <p class="text-muted mt-2">
                                <?php if (!empty($search_term)): ?>
                                    Nenhum produto encontrado para "<?= htmlspecialchars($search_term) ?>".
                                <?php else: ?>
                                    Todos os produtos ativos já estão na lista de recomendados.
                                <?php endif; ?>
                            </p>
                        </div>
                    <?php else: ?>
                        <div class="row">
                            <?php foreach ($available_products as $product): ?>
                                <div class="col-md-6 col-lg-4 mb-3">
                                    <div class="card h-100">
                                        <?php if ($product['display_image_url']): ?>
                                            <img src="<?= htmlspecialchars($product['display_image_url']) ?>" 
                                                 class="card-img-top" 
                                                 style="height: 150px; object-fit: cover;" 
                                                 alt="<?= htmlspecialchars($product['name_pt']) ?>">
                                        <?php else: ?>
                                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center" style="height: 150px;">
                                                <i class="bi bi-image text-muted" style="font-size: 2rem;"></i>
                                            </div>
                                        <?php endif; ?>
                                        <div class="card-body d-flex flex-column">
                                            <h6 class="card-title"><?= htmlspecialchars($product['name_pt']) ?></h6>
                                            <p class="card-text text-muted small">
                                                SKU: <?= htmlspecialchars($product['sku']) ?><br>
                                                Tipo: <?= ucfirst($product['product_type']) ?><br>
                                                Preço: €<?= number_format($product['base_price'], 2, ',', '.') ?>
                                            </p>
                                            <div class="mt-auto">
                                                <button type="button" 
                                                        class="btn btn-success btn-sm w-100 add-recommended-btn" 
                                                        data-product-id="<?= $product['id'] ?>">
                                                    <i class="bi bi-star"></i> Adicionar aos Recomendados
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add recommended product
    document.querySelectorAll('.add-recommended-btn').forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.dataset.productId;
            const originalText = this.innerHTML;

            this.disabled = true;
            this.innerHTML = '<i class="bi bi-hourglass-split"></i> Adicionando...';

            fetch('admin.php?section=recommended_products&ajax=1&<?= get_session_id_param() ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `action=add_recommended&product_id=${productId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    showAlert(data.message, 'success');
                    // Reload page to update lists
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    showAlert(data.message, 'danger');
                    this.disabled = false;
                    this.innerHTML = originalText;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('Erro ao adicionar produto aos recomendados.', 'danger');
                this.disabled = false;
                this.innerHTML = originalText;
            });
        });
    });

    // Remove recommended product
    document.querySelectorAll('.remove-recommended-btn').forEach(button => {
        button.addEventListener('click', function() {
            const productId = this.dataset.productId;

            if (!confirm('Tem certeza que deseja remover este produto dos recomendados?')) {
                return;
            }

            const originalText = this.innerHTML;
            this.disabled = true;
            this.innerHTML = '<i class="bi bi-hourglass-split"></i>';

            fetch('admin.php?section=recommended_products&ajax=1&<?= get_session_id_param() ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: `action=remove_recommended&product_id=${productId}`
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    showAlert(data.message, 'success');
                    // Remove the product card from the DOM
                    const productCard = this.closest('[data-product-id]');
                    if (productCard) {
                        productCard.style.transition = 'opacity 0.3s';
                        productCard.style.opacity = '0';
                        setTimeout(() => {
                            productCard.remove();
                            // Update count
                            updateRecommendedCount();
                        }, 300);
                    }
                } else {
                    showAlert(data.message, 'danger');
                    this.disabled = false;
                    this.innerHTML = originalText;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showAlert('Erro ao remover produto dos recomendados.', 'danger');
                this.disabled = false;
                this.innerHTML = originalText;
            });
        });
    });

    // Helper function to show alerts
    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        const container = document.querySelector('.container-fluid .row .col-12');
        const firstCard = container.querySelector('.card');
        container.insertBefore(alertDiv, firstCard);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    // Helper function to update recommended count
    function updateRecommendedCount() {
        const recommendedList = document.getElementById('recommended-products-list');
        const count = recommendedList ? recommendedList.children.length : 0;
        const countElement = document.querySelector('.text-muted i.bi-star').parentNode;
        if (countElement) {
            countElement.innerHTML = `<i class="bi bi-star"></i> ${count} produto${count !== 1 ? 's' : ''} recomendado${count !== 1 ? 's' : ''}`;
        }

        // Show empty state if no products
        if (count === 0 && recommendedList) {
            recommendedList.parentNode.innerHTML = `
                <div class="text-center py-4">
                    <i class="bi bi-star display-4 text-muted"></i>
                    <p class="text-muted mt-2">Nenhum produto recomendado ainda.</p>
                    <p class="text-muted">Use a seção abaixo para adicionar produtos à lista de recomendados.</p>
                </div>
            `;
        }
    }
});
</script>
