<?php
// Test file for recommended products functionality
// This file should be deleted after testing

require_once __DIR__ . '/includes/db.php';
require_once __DIR__ . '/includes/recommended_products_functions.php';

echo "<h1>Testing Recommended Products Functionality</h1>";

// Test database connection
echo "<h2>1. Testing Database Connection</h2>";
$pdo = get_db_connection();
if ($pdo) {
    echo "✅ Database connection successful<br>";
} else {
    echo "❌ Database connection failed<br>";
    exit;
}

// Test if recommended_products table exists
echo "<h2>2. Testing Database Table</h2>";
try {
    $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table' AND name='recommended_products';");
    $table_exists = $stmt->fetch();
    
    if ($table_exists) {
        echo "✅ recommended_products table exists<br>";
        
        // Show table structure
        $stmt = $pdo->query("PRAGMA table_info(recommended_products);");
        $columns = $stmt->fetchAll();
        echo "Table structure:<br>";
        foreach ($columns as $column) {
            echo "- {$column['name']} ({$column['type']})<br>";
        }
    } else {
        echo "❌ recommended_products table does not exist<br>";
    }
} catch (Exception $e) {
    echo "❌ Error checking table: " . $e->getMessage() . "<br>";
}

// Test getting products available for recommendation
echo "<h2>3. Testing Available Products Function</h2>";
try {
    $available_products = get_products_available_for_recommendation('', 5);
    echo "✅ Found " . count($available_products) . " products available for recommendation<br>";
    
    if (!empty($available_products)) {
        echo "Sample products:<br>";
        foreach (array_slice($available_products, 0, 3) as $product) {
            echo "- ID: {$product['id']}, Name: {$product['name_pt']}<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Error getting available products: " . $e->getMessage() . "<br>";
}

// Test getting current recommended products
echo "<h2>4. Testing Current Recommended Products</h2>";
try {
    $recommended_products = get_recommended_products();
    echo "✅ Found " . count($recommended_products) . " recommended products<br>";
    
    if (!empty($recommended_products)) {
        echo "Current recommended products:<br>";
        foreach ($recommended_products as $product) {
            echo "- ID: {$product['product_id']}, Name: {$product['name_pt']}, Order: {$product['display_order']}<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Error getting recommended products: " . $e->getMessage() . "<br>";
}

// Test adding a recommended product (if we have available products)
echo "<h2>5. Testing Add Recommended Product</h2>";
try {
    $available_products = get_products_available_for_recommendation('', 1);
    if (!empty($available_products)) {
        $test_product_id = $available_products[0]['id'];
        $result = add_recommended_product($test_product_id);
        
        if ($result) {
            echo "✅ Successfully added product ID {$test_product_id} to recommended products<br>";
            
            // Test removing it
            $remove_result = remove_recommended_product($test_product_id);
            if ($remove_result) {
                echo "✅ Successfully removed product ID {$test_product_id} from recommended products<br>";
            } else {
                echo "❌ Failed to remove product ID {$test_product_id} from recommended products<br>";
            }
        } else {
            echo "❌ Failed to add product ID {$test_product_id} to recommended products<br>";
        }
    } else {
        echo "ℹ️ No products available to test adding to recommended products<br>";
    }
} catch (Exception $e) {
    echo "❌ Error testing add/remove recommended product: " . $e->getMessage() . "<br>";
}

// Test random ordering
echo "<h2>6. Testing Random Ordering</h2>";
try {
    $random_products_1 = get_recommended_products_random();
    $random_products_2 = get_recommended_products_random();
    
    if (count($random_products_1) > 1) {
        $order_1 = array_column($random_products_1, 'product_id');
        $order_2 = array_column($random_products_2, 'product_id');
        
        if ($order_1 !== $order_2) {
            echo "✅ Random ordering is working (orders are different)<br>";
        } else {
            echo "ℹ️ Random ordering may be working (orders are same, but this can happen by chance)<br>";
        }
    } else {
        echo "ℹ️ Need at least 2 recommended products to test random ordering<br>";
    }
} catch (Exception $e) {
    echo "❌ Error testing random ordering: " . $e->getMessage() . "<br>";
}

echo "<h2>Testing Complete</h2>";
echo "<p><strong>Note:</strong> This test file should be deleted after testing is complete.</p>";
echo "<p><a href='admin.php?section=recommended_products'>Go to Admin Interface</a></p>";
echo "<p><a href='index.php'>Go to Homepage</a></p>";
?>
