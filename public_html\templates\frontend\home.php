<?php

$sort_options = [
    'recent' => 'created_at DESC',
    'price_asc' => 'base_price ASC',
    'price_desc' => 'base_price DESC',
    
];
$current_sort = isset($_GET['sort']) && array_key_exists($_GET['sort'], $sort_options) ? $_GET['sort'] : 'recent';
$order_by_clause = $sort_options[$current_sort];

$category_id = filter_input(INPUT_GET, 'category_id', FILTER_VALIDATE_INT);
$category_id = ($category_id !== false && $category_id > 0) ? $category_id : 0; 

$items_per_page = get_setting('items_per_page', 12); 
$current_page = filter_input(INPUT_GET, 'pg', FILTER_VALIDATE_INT);
$current_page = ($current_page !== false && $current_page > 0) ? $current_page : 1; 
$offset = ($current_page - 1) * $items_per_page;

$min_price_filter = filter_input(INPUT_GET, 'min_price', FILTER_VALIDATE_FLOAT);
$max_price_filter = filter_input(INPUT_GET, 'max_price', FILTER_VALIDATE_FLOAT);

$current_filter_state = [
    'category_id' => $category_id,
    'sort' => $current_sort,
    'current_page' => $current_page,
    'min_price' => $min_price_filter !== false ? $min_price_filter : null,
    'max_price' => $max_price_filter !== false ? $max_price_filter : null,
    'total_pages' => 0 
];

$sql_select = "SELECT p.id, p.name_pt, p.slug, p.base_price";
$sql_from = " FROM products p ";
$sql_where = " WHERE p.is_active = 1 ";
$sql_order_by = " ORDER BY " . $order_by_clause; 
$sql_limit_offset = " LIMIT :limit OFFSET :offset "; 
$params = [':limit' => $items_per_page, ':offset' => $offset]; 

if ($category_id > 0) {
    $sql_from .= " JOIN product_categories pc ON p.id = pc.product_id ";
    $sql_where .= " AND pc.category_id = :category_id ";
    $params[':category_id'] = $category_id;
}

if ($min_price_filter !== false && $min_price_filter !== null) {
    $sql_where .= " AND p.base_price >= :min_price ";
    $params[':min_price'] = $min_price_filter;
}
if ($max_price_filter !== false && $max_price_filter !== null) {
    $sql_where .= " AND p.base_price <= :max_price ";
    $params[':max_price'] = $max_price_filter;
}

$sql_count = "SELECT COUNT(p.id)" . $sql_from . $sql_where;

$count_params = [];
if ($category_id > 0) {
    $count_params[':category_id'] = $category_id;
}
if ($min_price_filter !== false && $min_price_filter !== null) {
    $count_params[':min_price'] = $min_price_filter;
}
if ($max_price_filter !== false && $max_price_filter !== null) {
    $count_params[':max_price'] = $max_price_filter;
}
$total_products_stmt = db_query($sql_count, $count_params);
$total_products = ($total_products_stmt) ? (int)$total_products_stmt->fetchColumn() : 0;
$total_pages = ceil($total_products / $items_per_page);

$current_filter_state['total_pages'] = $total_pages;

$sql_data = $sql_select . $sql_from . $sql_where . $sql_order_by . $sql_limit_offset;

$products = db_query($sql_data, $params, false, true);

if ($products && count($products) > 0) {
    foreach ($products as &$product) {
        $product['display_image_url'] = null;
        $default_image = get_product_default_image($product['id']);
        if ($default_image && !empty($default_image['filename'])) {
            $product['display_image_url'] = get_product_image_url($default_image['filename']);
        }
    }
    unset($product);
} else {
    $products = []; 
}

$categories = getAllCategories();
?>

<!-- Sort Controls are now in the collapsible_filters.php partial -->
<!-- The PHP logic for $current_sort and $order_by_clause (lines 3-10) remains here as it's needed for the product query -->

<?php

require_once __DIR__ . '/../../includes/banner_display.php';
echo display_wide_banner('mb-6');
?>

<!-- Products Grid - Improved Responsive Layout -->
<div id="products-grid" class="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4 md:gap-6">
    <?php if (count($products) > 0): ?>
        <?php foreach ($products as $product):
            
            include __DIR__ . '/partials/product_card.php';
        endforeach; ?>
    <?php else: ?>
        <div class="col-span-full text-center py-12">
            <div class="w-20 h-20 sm:w-24 sm:h-24 mx-auto mb-4 sm:mb-6 flex items-center justify-center bg-gray-800 rounded-full overflow-hidden">
                <?php
                
                $logo_path = get_setting('store_logo_path');
                $logo_full_path = !empty($logo_path) ? PROJECT_ROOT . '/' . $logo_path : null;
                if ($logo_full_path && file_exists($logo_full_path)) {
                    $logo_url = BASE_URL . '/' . $logo_path . '?' . filemtime($logo_full_path); 
                    echo '<img src="' . sanitize_input($logo_url) . '" alt="' . sanitize_input(get_setting('store_name')) . ' Logo" class="w-12 h-12 sm:w-16 sm:h-16 object-contain">';
                } else {
                    
                    echo '<i class="ri-store-2-line text-3xl sm:text-4xl text-gray-400"></i>';
                }
                ?>
            </div>
            <p class="text-lg sm:text-xl text-gray-400">Não existem produtos disponíveis de momento.</p>
            <p class="text-sm sm:text-base text-gray-500">Volte em breve!</p>
        </div>
    <?php endif; ?>
</div>

<!-- Dynamic Pagination - Responsive -->
<?php if ($total_pages > 1): ?>
<div id="pagination-container" class="mt-8 sm:mt-10 md:mt-12 flex justify-center">
    <nav class="inline-flex flex-wrap justify-center rounded-md shadow-sm bg-gray-800" aria-label="Pagination">
        <?php
        
        $query_params = $_GET; 
        unset($query_params['pg']); 
        

        
        if ($category_id > 0) {
            $query_params['category_id'] = $category_id;
        }
        if ($current_sort !== 'recent') {
            $query_params['sort'] = $current_sort;
        }

        $base_url = 'index.php?' . http_build_query($query_params) . '&'; 

        
        $prev_page = $current_page - 1;
        $prev_link_class = 'px-2 sm:px-3 py-1.5 sm:py-2 text-xs sm:text-sm font-medium rounded-l-button ';
        $prev_link_href = $base_url . 'pg=' . $prev_page;
        if ($current_page <= 1) {
            $prev_link_class .= 'text-gray-600 bg-gray-700 cursor-not-allowed opacity-50'; 
            $prev_tag = 'span';
            $prev_link_href = '#'; 
        } else {
            $prev_link_class .= 'text-gray-300 hover:bg-gray-700'; 
            $prev_tag = 'a';
        }
        ?>
        <<?= $prev_tag ?> href="<?= $prev_link_href ?>" class="<?= $prev_link_class ?>" <?= ($prev_tag === 'a' ? 'aria-label="Previous"' : '') ?>>
            <i class="ri-arrow-left-s-line"></i>
        </<?= $prev_tag ?>>

        <?php
        
        
        $max_visible_pages = 5; 
        $half_max = floor($max_visible_pages / 2);

        
        $start_page = max(1, $current_page - $half_max);
        $end_page = min($total_pages, $start_page + $max_visible_pages - 1);

        
        if ($end_page - $start_page + 1 < $max_visible_pages) {
            $start_page = max(1, $end_page - $max_visible_pages + 1);
        }

        
        if ($start_page > 1) {
            $first_link_class = 'px-2 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm font-medium text-gray-300 hover:bg-gray-700';
            $first_link_href = $base_url . 'pg=1';
        ?>
            <a href="<?= $first_link_href ?>" class="<?= $first_link_class ?>">1</a>

            <?php if ($start_page > 2): ?>
            <span class="px-1 sm:px-2 py-1.5 sm:py-2 text-xs sm:text-sm font-medium text-gray-500">...</span>
            <?php endif; ?>
        <?php
        }

        
        for ($i = $start_page; $i <= $end_page; $i++):
            $page_link_class = 'px-2 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm font-medium ';
            $page_link_href = $base_url . 'pg=' . $i;
            $page_tag = 'a';
            $aria_current = '';

            if ($i == $current_page) {
                $page_link_class .= 'text-white bg-primary z-10 cursor-default'; 
                $page_tag = 'span'; 
                $aria_current = 'aria-current="page"';
                $page_link_href = '#'; 
            } else {
                $page_link_class .= 'text-gray-300 hover:bg-gray-700'; 
            }
        ?>
            <<?= $page_tag ?> href="<?= $page_link_href ?>" class="<?= $page_link_class ?>" <?= $aria_current ?>>
                <?= $i ?>
            </<?= $page_tag ?>>
        <?php endfor; ?>

        <?php
        
        if ($end_page < $total_pages) {
            if ($end_page < $total_pages - 1): ?>
            <span class="px-1 sm:px-2 py-1.5 sm:py-2 text-xs sm:text-sm font-medium text-gray-500">...</span>
            <?php endif;

            $last_link_class = 'px-2 sm:px-4 py-1.5 sm:py-2 text-xs sm:text-sm font-medium text-gray-300 hover:bg-gray-700';
            $last_link_href = $base_url . 'pg=' . $total_pages;
        ?>
            <a href="<?= $last_link_href ?>" class="<?= $last_link_class ?>"><?= $total_pages ?></a>
        <?php
        }

        
        $next_page = $current_page + 1;
        $next_link_class = 'px-2 sm:px-3 py-1.5 sm:py-2 text-xs sm:text-sm font-medium rounded-r-button ';
        $next_link_href = $base_url . 'pg=' . $next_page;
        if ($current_page >= $total_pages) {
            $next_link_class .= 'text-gray-600 bg-gray-700 cursor-not-allowed opacity-50'; 
            $next_tag = 'span';
            $next_link_href = '#'; 
        } else {
            $next_link_class .= 'text-gray-300 hover:bg-gray-700'; 
            $next_tag = 'a';
        }
        ?>
        <<?= $next_tag ?> href="<?= $next_link_href ?>" class="<?= $next_link_class ?>" <?= ($next_tag === 'a' ? 'aria-label="Next"' : '') ?>>
             <i class="ri-arrow-right-s-line"></i>
        </<?= $next_tag ?>>
    </nav>
</div>
<?php endif; ?>

<?php

require_once __DIR__ . '/../../includes/banner_display.php';
echo display_homepage_content_banner('mt-16 mb-6');
?>

<!-- Latest Blog Posts Section -->
<?php if (!empty($latest_blog_posts)): ?>
<div class="mt-16 pt-12">
    <!-- Blog Posts Slider -->
    <div class="blog-slider-container relative mb-8">
        <div class="blog-slider overflow-hidden">
            <div class="blog-slider-track flex transition-transform duration-500" id="blogSliderTrack">
                <?php foreach ($latest_blog_posts as $index => $post):
                    $post_url = add_session_param_to_url(BASE_URL . '/index.php?view=blog_post&slug=' . $post['slug']);
                    $image_full_path = !empty($post['image_path']) ? PROJECT_ROOT . '/' . $post['image_path'] : null;
                    $image_url = ($image_full_path && file_exists($image_full_path))
                                ? BASE_URL . '/' . $post['image_path'] . '?' . filemtime($image_full_path) 
                                : null; 

                    
                    if (!$image_url && $post['post_type'] === 'link') {
                        $placeholder_icon = '<i class="ri-link ri-4x text-gray-500"></i>';
                    } else {
                        $placeholder_icon = null;
                    }
                ?>
                    <div class="blog-slide flex-shrink-0 w-full md:w-1/2 lg:w-1/3 px-3">
                        <div class="bg-gray-800 rounded-lg overflow-hidden shadow-lg flex flex-col h-full transition duration-300 hover:shadow-primary/50">
                            <?php if ($post['post_type'] === 'link'): ?>
                                <a href="<?php echo htmlspecialchars($post['link_url'] ?? '#'); ?>" target="_blank" rel="noopener noreferrer" class="block">
                                    <div class="relative">
                                        <?php if ($image_url): ?>
                                            <img src="<?php echo htmlspecialchars($image_url); ?>" alt="<?php echo htmlspecialchars($post['title']); ?>" class="w-full h-48 object-cover blog-post-image rounded-t-lg">
                                            <?php if (!empty($post['image_description'])): ?>
                                                <div class="absolute bottom-0 right-0 bg-black bg-opacity-60 text-white text-xs italic p-1 rounded-tl-md">
                                                    <?php echo htmlspecialchars($post['image_description']); ?>
                                                </div>
                                            <?php endif; ?>
                                        <?php elseif ($placeholder_icon): ?>
                                            <div class="w-full h-48 flex items-center justify-center bg-gray-700 rounded-t-lg">
                                                <?php echo $placeholder_icon; ?>
                                            </div>
                                        <?php else: ?>
                                            <div class="w-full h-48 bg-gray-700 rounded-t-lg"></div>
                                        <?php endif; ?>

                                        <!-- Link type indicator with animation -->
                                        <div class="absolute top-3 left-3 bg-primary bg-opacity-90 text-white text-xs font-medium px-2 py-1 rounded-full flex items-center shadow-md backdrop-blur-sm transform transition-transform duration-300 hover:scale-105">
                                            <i class="ri-external-link-line mr-1"></i>
                                            <span>Link Externo</span>
                                        </div>
                                    </div>
                                </a>
                            <?php else: ?>
                                <a href="<?php echo $post_url; ?>" class="block">
                                    <div class="relative">
                                        <?php if ($image_url): ?>
                                            <img src="<?php echo htmlspecialchars($image_url); ?>" alt="<?php echo htmlspecialchars($post['title']); ?>" class="w-full h-48 object-cover blog-post-image rounded-t-lg">
                                            <?php if (!empty($post['image_description'])): ?>
                                                <div class="absolute bottom-0 right-0 bg-black bg-opacity-60 text-white text-xs italic p-1 rounded-tl-md">
                                                    <?php echo htmlspecialchars($post['image_description']); ?>
                                                </div>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <div class="w-full h-48 bg-gray-700 rounded-t-lg"></div>
                                        <?php endif; ?>
                                        <?php if ($post['post_type'] === 'CODE'): ?>
                                        <!-- CODE type indicator badge -->
                                        <div class="absolute top-3 left-3 bg-purple-600 bg-opacity-90 text-white text-xs font-medium px-2 py-1 rounded-full flex items-center shadow-md backdrop-blur-sm transform transition-transform duration-300 hover:scale-105">
                                            <i class="ri-code-s-slash-line mr-1"></i>
                                            <span>Especial</span>
                                        </div>
                                        <?php endif; ?>
                                    </div>
                                </a>
                            <?php endif; ?>

                            <div class="p-4 flex flex-col flex-grow">
                                <h3 class="text-lg font-semibold mb-2 text-white">
                                    <?php if ($post['post_type'] === 'link'): ?>
                                        <a href="<?php echo htmlspecialchars($post['link_url'] ?? '#'); ?>" target="_blank" rel="noopener noreferrer" class="hover:text-primary transition group flex items-center">
                                            <span><?php echo htmlspecialchars($post['title']); ?></span>
                                            <i class="ri-external-link-line text-sm align-middle ml-1 transform transition-transform duration-300 group-hover:translate-x-1 group-hover:translate-y-[-2px]"></i>
                                        </a>
                                    <?php else: ?>
                                        <a href="<?php echo $post_url; ?>" class="hover:text-primary transition">
                                            <?php echo htmlspecialchars($post['title']); ?>
                                        </a>
                                    <?php endif; ?>
                                </h3>
                                <p class="text-xs text-gray-400 mb-3">
                                    Publicado em <?php echo format_date($post['published_at']); ?>
                                </p>
                                <?php if ($post['post_type'] === 'article'): ?>
                                    <p class="text-gray-300 text-sm mb-4 flex-grow">
                                        <?php echo htmlspecialchars(limit_words(strip_tags($post['content'] ?? ''), 20)); ?>...
                                    </p>
                                    <a href="<?php echo $post_url; ?>" class="text-primary hover:underline text-sm mt-auto">Ler Mais</a>
                                <?php elseif ($post['post_type'] === 'link'): ?>
                                    <?php if (!empty($post['link_description'])): ?>
                                        <p class="text-gray-300 text-sm mb-4 flex-grow"><?php echo nl2br(htmlspecialchars($post['link_description'])); ?></p>
                                    <?php else: ?>
                                        <div class="flex-grow"></div>
                                    <?php endif; ?>
                                    <a href="<?php echo htmlspecialchars($post['link_url'] ?? '#'); ?>" target="_blank" rel="noopener noreferrer" class="text-primary hover:text-secondary text-sm mt-auto inline-flex items-center group">
                                        <span>Visitar Link</span>
                                        <i class="ri-external-link-line text-xs align-middle ml-1 transform transition-transform duration-300 group-hover:translate-x-1 group-hover:translate-y-[-1px]"></i>
                                    </a>
                                <?php elseif ($post['post_type'] === 'CODE'): ?>
                                    <?php if (!empty($post['link_description'])): ?>
                                        <p class="text-gray-300 text-sm mb-4 flex-grow"><?php echo nl2br(htmlspecialchars($post['link_description'])); ?></p>
                                    <?php else: ?>
                                        <div class="flex-grow"></div>
                                    <?php endif; ?>
                                    <a href="<?php echo $post_url; ?>" class="text-primary hover:underline text-sm mt-auto inline-flex items-center group">
                                        <span>Ver Conteúdo</span>
                                        <i class="ri-file-text-line text-xs align-middle ml-1"></i>
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Slider Navigation -->
        <button class="blog-slider-prev absolute top-1/2 left-0 transform -translate-y-1/2 bg-primary text-white w-10 h-10 rounded-full flex items-center justify-center focus:outline-none z-10">
            <i class="ri-arrow-left-s-line text-xl"></i>
        </button>
        <button class="blog-slider-next absolute top-1/2 right-0 transform -translate-y-1/2 bg-primary text-white w-10 h-10 rounded-full flex items-center justify-center focus:outline-none z-10">
            <i class="ri-arrow-right-s-line text-xl"></i>
        </button>

        <!-- Slider Dots -->
        <div class="blog-slider-dots flex justify-center mt-4">
            <?php foreach ($latest_blog_posts as $index => $post): ?>
                <button class="blog-slider-dot w-3 h-3 rounded-full bg-gray-600 mx-1 focus:outline-none <?= $index === 0 ? 'active bg-primary' : '' ?>" data-index="<?= $index ?>"></button>
            <?php endforeach; ?>
        </div>
    </div>

    <div class="text-center mt-8">
       <a href="<?= add_session_param_to_url(BASE_URL . '/index.php?view=blog') ?>" class="inline-block bg-primary text-white px-6 py-2 rounded-button hover:bg-secondary transition">Ver Todos os Posts</a>
    </div>
</div>

<!-- Blog Slider JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const track = document.getElementById('blogSliderTrack');
    const slides = document.querySelectorAll('.blog-slide');
    const dots = document.querySelectorAll('.blog-slider-dot');
    const prevButton = document.querySelector('.blog-slider-prev');
    const nextButton = document.querySelector('.blog-slider-next');

    if (!track || slides.length === 0) return;

    // Create a true infinite loop by duplicating slides at both ends
    function setupInfiniteLoop() {
        // Get original slides
        const originalSlides = Array.from(slides);
        const slideCount = originalSlides.length;

        // If we have only one slide, no need for infinite loop
        if (slideCount <= 1) return;

        // Clone the last slide and add it to the beginning
        const lastSlideClone = originalSlides[slideCount - 1].cloneNode(true);
        lastSlideClone.classList.add('cloned-slide');
        track.insertBefore(lastSlideClone, track.firstChild);

        // Clone the first slide and add it to the end
        const firstSlideClone = originalSlides[0].cloneNode(true);
        firstSlideClone.classList.add('cloned-slide');
        track.appendChild(firstSlideClone);
    }

    // Setup infinite loop
    setupInfiniteLoop();

    // Get all slides after cloning
    const allSlides = document.querySelectorAll('.blog-slide');
    const originalSlideCount = slides.length;
    const totalSlideCount = allSlides.length;

    // Start at index 1 (which is the first real slide after the clone)
    let currentIndex = 1;
    let slideWidth = allSlides[0].offsetWidth;
    let autoplayInterval;
    const autoplayDelay = <?= $blog_slider_delay ?? 5 ?> * 1000; // Convert seconds to milliseconds
    let isTransitioning = false;

    // Function to update slide positions
    function updateSlidePosition(skipTransition = false) {
        if (skipTransition) {
            track.style.transition = 'none';
        } else {
            track.style.transition = 'transform 0.5s ease';
        }

        track.style.transform = `translateX(-${currentIndex * slideWidth}px)`;

        // Update active dot - map to original slide index (0-based)
        const activeDotIndex = (currentIndex - 1 + originalSlideCount) % originalSlideCount;
        dots.forEach((dot, index) => {
            if (index === activeDotIndex) {
                dot.classList.add('active', 'bg-primary');
                dot.classList.remove('bg-gray-600');
            } else {
                dot.classList.remove('active', 'bg-primary');
                dot.classList.add('bg-gray-600');
            }
        });

        if (skipTransition) {
            // Force reflow to ensure the transition is applied
            track.offsetHeight;
            track.style.transition = 'transform 0.5s ease';
        }
    }

    // Function to go to next slide
    function nextSlide() {
        if (isTransitioning) return;
        isTransitioning = true;

        currentIndex++;
        updateSlidePosition();
    }

    // Function to go to previous slide
    function prevSlide() {
        if (isTransitioning) return;
        isTransitioning = true;

        currentIndex--;
        updateSlidePosition();
    }

    // Function to go to a specific slide (dot navigation)
    function goToSlide(index) {
        if (isTransitioning) return;
        isTransitioning = true;

        // +1 because we have a clone at the beginning
        currentIndex = index + 1;
        updateSlidePosition();
    }

    // Start autoplay
    function startAutoplay() {
        stopAutoplay(); // Clear any existing interval
        autoplayInterval = setInterval(nextSlide, autoplayDelay);
    }

    // Stop autoplay
    function stopAutoplay() {
        if (autoplayInterval) {
            clearInterval(autoplayInterval);
        }
    }

    // Event listeners for navigation
    if (prevButton) {
        prevButton.addEventListener('click', function() {
            prevSlide();
            stopAutoplay();
            startAutoplay(); // Restart autoplay after manual navigation
        });
    }

    if (nextButton) {
        nextButton.addEventListener('click', function() {
            nextSlide();
            stopAutoplay();
            startAutoplay(); // Restart autoplay after manual navigation
        });
    }

    // Event listeners for dots
    dots.forEach((dot, index) => {
        dot.addEventListener('click', function() {
            goToSlide(index);
            stopAutoplay();
            startAutoplay(); // Restart autoplay after manual navigation
        });
    });

    // Handle window resize
    function handleResize() {
        slideWidth = allSlides[0].offsetWidth;
        updateSlidePosition(true);
    }

    window.addEventListener('resize', handleResize);

    // Handle transition end - implement the infinite loop effect
    track.addEventListener('transitionend', function() {
        isTransitioning = false;

        // If we've reached the clone at the end, jump to the first real slide
        if (currentIndex >= totalSlideCount - 1) {
            currentIndex = 1;
            updateSlidePosition(true);
        }
        // If we've reached the clone at the beginning, jump to the last real slide
        else if (currentIndex <= 0) {
            currentIndex = totalSlideCount - 2;
            updateSlidePosition(true);
        }
    });

    // Initialize slider - position at first real slide
    updateSlidePosition(true);
    startAutoplay();

    // Pause autoplay when hovering over slider
    const sliderContainer = document.querySelector('.blog-slider-container');
    if (sliderContainer) {
        sliderContainer.addEventListener('mouseenter', stopAutoplay);
        sliderContainer.addEventListener('mouseleave', startAutoplay);
    }
});
</script>

<style>
.blog-slider {
    position: relative;
    overflow: hidden;
    padding: 0 40px;
}

.blog-slider-track {
    display: flex;
    transition: transform 0.5s ease;
}

.blog-slider-dot.active {
    transform: scale(1.2);
}

@media (max-width: 768px) {
    .blog-slide {
        width: 100%;
    }
}
</style>
<?php endif; ?>
<!-- End Latest Blog Posts Section -->

<!-- Recommended Products Section -->
<?php include __DIR__ . '/partials/recommended_products_slider.php'; ?>
<!-- End Recommended Products Section -->

<!-- JavaScript for Filter State Management -->
<script>
// Initialize filter state from PHP
const initialFilterState = <?= json_encode($current_filter_state) ?>;

document.addEventListener('DOMContentLoaded', function() {
    // Set initial active category button based on URL parameter
    const categoryId = initialFilterState.category_id.toString();
    const categoryButtons = document.querySelectorAll('.category-filter-btn');
    const categoryContainer = document.querySelector('.category-filter-container');
    const sortSelect = document.getElementById('product-sort-select');

    // Function to update URL with current filter state
    function updateUrlWithFilters(newCategoryId, newSort, newPage = 1) {
        const url = new URL(window.location.href);

        // No need to set view=home as it's the default

        // Update category parameter
        if (newCategoryId && newCategoryId !== '0') {
            url.searchParams.set('category_id', newCategoryId);
        } else {
            url.searchParams.delete('category_id');
        }

        // Update sort parameter
        if (newSort && newSort !== 'recent') {
            url.searchParams.set('sort', newSort);
        } else {
            url.searchParams.delete('sort');
        }

        // Update page parameter
        if (newPage > 1) {
            url.searchParams.set('pg', newPage);
        } else {
            url.searchParams.delete('pg');
        }

        // Update browser history without reloading
        history.pushState({}, '', url);
    }

    // Set initial active category button
    categoryButtons.forEach(button => {
        if (button.dataset.categoryId === categoryId) {
            // Set this button as active
            document.querySelector('.active-category')?.classList.remove('active-category', 'bg-primary');
            document.querySelector('.active-category')?.classList.add('bg-gray-800', 'hover:bg-gray-700');
            button.classList.add('active-category', 'bg-primary');
            button.classList.remove('bg-gray-800', 'hover:bg-gray-700');
        }

        // Add click handler for category buttons
        button.addEventListener('click', function() {
            const newCategoryId = this.dataset.categoryId;
            if (newCategoryId !== categoryId) {
                // Update active state visually
                document.querySelector('.active-category')?.classList.remove('active-category', 'bg-primary');
                document.querySelector('.active-category')?.classList.add('bg-gray-800', 'hover:bg-gray-700');
                this.classList.add('active-category', 'bg-primary');
                this.classList.remove('bg-gray-800', 'hover:bg-gray-700');

                // Get current sort and price filter values from initialFilterState
                const currentSort = initialFilterState.sort;
                const currentMinPrice = initialFilterState.min_price;
                const currentMaxPrice = initialFilterState.max_price;

                // Update URL and reload page
                const url = new URL(window.location.href);
                
                if (newCategoryId && newCategoryId !== '0') {
                    url.searchParams.set('category_id', newCategoryId);
                } else {
                    url.searchParams.delete('category_id');
                }
                
                // Preserve sort
                if (currentSort && currentSort !== 'recent') {
                    url.searchParams.set('sort', currentSort);
                } else {
                     url.searchParams.delete('sort');
                }
                // Preserve price filters
                if (currentMinPrice !== null) {
                    url.searchParams.set('min_price', currentMinPrice);
                } else {
                    url.searchParams.delete('min_price');
                }
                if (currentMaxPrice !== null) {
                    url.searchParams.set('max_price', currentMaxPrice);
                } else {
                    url.searchParams.delete('max_price');
                }

                url.searchParams.delete('pg'); // Reset to page 1
                window.location.href = url.toString();
            }
        });
    });

    // The sortSelect element and its event listener have been removed from home.php
    // as sorting is now handled by the collapsible_filters.php partial.
    // This script block in home.php is now primarily for:
    // 1. Setting the initial active category button (if category_id is in URL) - this is already done above.
    // 2. Ensuring category button clicks preserve other filters (sort, price) when changing category.
    // 3. Ensuring pagination clicks preserve all filters.

    // Add click handler for pagination links to maintain filter state
    const paginationContainer = document.getElementById('pagination-container');
    if (paginationContainer) {
        paginationContainer.addEventListener('click', function(e) {
            // Only handle clicks on pagination links (not on the current page span)
            const link = e.target.closest('a[href]');
            if (link && !link.getAttribute('aria-current')) {
                e.preventDefault();

                // Get the URL from the link
                const url = new URL(link.href, window.location.origin);
                const newPage = url.searchParams.get('pg');

                // Ensure category_id is preserved if it exists
                if (categoryId !== '0') {
                    url.searchParams.set('category_id', categoryId);
                }

                // Ensure sort is preserved
                if (initialFilterState.sort && initialFilterState.sort !== 'recent') {
                    url.searchParams.set('sort', initialFilterState.sort);
                } else {
                    url.searchParams.delete('sort');
                }
                // Ensure price filters are preserved
                if (initialFilterState.min_price !== null) {
                    url.searchParams.set('min_price', initialFilterState.min_price);
                } else {
                    url.searchParams.delete('min_price');
                }
                if (initialFilterState.max_price !== null) {
                    url.searchParams.set('max_price', initialFilterState.max_price);
                } else {
                    url.searchParams.delete('max_price');
                }

                // Navigate to the new URL
                window.location.href = url.toString();
            }
        });
    }
});
</script>
