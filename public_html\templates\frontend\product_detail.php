<?php

require_once __DIR__ . '/../../includes/product_functions.php';

if (!isset($product_slug)) {
    echo "<h1>Erro</h1><p>Produto não especificado.</p>";
    return;
}

require_once __DIR__ . '/../../includes/vat_functions.php';

echo '<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.css" />';
echo '<script src="https://cdn.jsdelivr.net/npm/@fancyapps/ui@4.0/dist/fancybox.umd.js"></script>';

$product = db_query(
    "SELECT p.*, v.rate as vat_rate, v.description as vat_description
     FROM products p
     LEFT JOIN vat_rates v ON p.vat_rate_id = v.id
     WHERE p.slug = :slug AND p.is_active = 1",
    [':slug' => $product_slug],
    true
);

if (!$product) {
    http_response_code(404);
    echo "<h1 class='text-2xl font-semibold mb-4'>Produto Não Encontrado</h1><p class='text-gray-400'>O produto que procura não existe ou não está disponível.</p>";
    return;
}

$page_title = sanitize_input($product['name_pt']) . " - " . sanitize_input(get_setting('store_name', 'Minha Loja'));

require_once __DIR__ . '/../../includes/custom_field_functions.php';
$product_custom_fields = get_product_custom_fields($product['id']);

$direct_variations = db_query(
    "SELECT id, sku, stock, is_active, price_modifier_override FROM product_variations WHERE product_id = :product_id AND is_active = 1",
    [':product_id' => $product['id']],
    false, true
);

$variation_stock_map = [];
foreach ($direct_variations as $var) {
    $variation_stock_map[$var['id']] = (int)$var['stock'];
}

$variations_raw = db_query(
    "SELECT
        pv.id as variation_id, pv.sku, pv.stock, pv.is_active as variation_is_active, pv.price_modifier_override,
        vv.value_id,
        av.value_pt, av.price_modifier as value_price_modifier, /* Removed non-existent av.image_filename */
        a.id as attribute_id, a.name_pt as attribute_name
     FROM product_variations pv
     JOIN variation_values vv ON pv.id = vv.variation_id
     JOIN attribute_values av ON vv.value_id = av.id
     JOIN attributes a ON av.attribute_id = a.id
     JOIN products p ON pv.product_id = p.id
     WHERE pv.product_id = :product_id AND pv.is_active = 1 AND p.is_active = 1
     ORDER BY pv.id",
    [':product_id' => $product['id']],
    false, true
);

$attributes = [];
$variation_data = [];
$attribute_value_modifiers = [];

$has_single_variation_with_stock = false;
$single_variation_id = null;
$single_variation_stock = 0;

if ($variations_raw) {
    foreach ($variations_raw as $row) {
        if (!isset($attributes[$row['attribute_id']])) {
            $attributes[$row['attribute_id']] = [
                'name' => $row['attribute_name'],
                'values' => []
            ];
        }
        if (!isset($attributes[$row['attribute_id']]['values'][$row['value_id']])) {
             $attributes[$row['attribute_id']]['values'][$row['value_id']] = $row['value_pt'];
        }
        if (!isset($variation_data[$row['variation_id']])) {

            $stock = $variation_stock_map[$row['variation_id']] ?? (is_numeric($row['stock']) ? (int)$row['stock'] : 0);

            $variation_data[$row['variation_id']] = [
                'sku' => $row['sku'],
                'stock' => $stock,
                'price_modifier_override' => $row['price_modifier_override'],
                'options' => []
            ];
        }
        $variation_data[$row['variation_id']]['options'][$row['attribute_id']] = $row['value_id'];
        if (!isset($attribute_value_modifiers[$row['value_id']])) {
             $attribute_value_modifiers[$row['value_id']] = (float)$row['value_price_modifier'];
        }

        if (!isset($single_variation_id)) {
            $single_variation_id = $row['variation_id'];

            $single_variation_stock = $variation_stock_map[$row['variation_id']] ?? (int)$row['stock'];
            $has_single_variation_with_stock = $single_variation_stock > 0;
        }

    }
} elseif ($product) {

    if ($direct_variations) {

        $first_var = $direct_variations[0];
        $variation_data[$first_var['id']] = [
            'sku' => $first_var['sku'] ?? '',
            'stock' => (int)$first_var['stock'],
            'price_modifier_override' => $first_var['price_modifier_override'],
            'options' => []
        ];
        $single_variation_id = $first_var['id'];
        $single_variation_stock = (int)$first_var['stock'];
        $has_single_variation_with_stock = $single_variation_stock > 0;

        if (empty($attributes)) {
            echo '<script>document.addEventListener("DOMContentLoaded", () => { window.singleVariationId = "' . $single_variation_id . '"; });</script>';
        }
    } else {

        $variation_data[0] = [
            'sku' => $product['sku'] ?? '',
            'stock' => 0,
            'options' => []
        ];

        if (empty($attributes)) {
            echo '<script>document.addEventListener("DOMContentLoaded", () => { window.singleVariationId = "0"; });</script>';
        }
    }
}

$min_total_variation_modifier = 0;
if (!empty($variation_data)) {
    $first_variation_processed = false;
    $current_min_for_variations = PHP_FLOAT_MAX;

    foreach ($variation_data as $var_id => $var_details) {
        $current_var_mod = 0;
        if (isset($var_details['price_modifier_override']) && $var_details['price_modifier_override'] !== null) {
            $current_var_mod = (float)$var_details['price_modifier_override'];
        } else {
            if (!empty($var_details['options'])) {
                foreach ($var_details['options'] as $attr_id_in_var => $value_id_in_var) {
                    if (isset($attribute_value_modifiers[$value_id_in_var])) {
                         $current_var_mod += (float)$attribute_value_modifiers[$value_id_in_var];
                    }
                }
            }
        }
        if (!$first_variation_processed || $current_var_mod < $current_min_for_variations) {
            $current_min_for_variations = $current_var_mod;
            $first_variation_processed = true;
        }
    }
    if ($first_variation_processed) {
        $min_total_variation_modifier = $current_min_for_variations;
    }
}

$min_total_custom_field_modifier = 0;
if (!empty($product_custom_fields)) {
    foreach ($product_custom_fields as $field) {
        $field_price_modifier_val = $field['price_modifier_override'] !== null ? $field['price_modifier_override'] : $field['price_modifier'];
        $field_modifier_float = (float)$field_price_modifier_val;

        if ($field['is_required']) {
            $min_total_custom_field_modifier += $field_modifier_float;
        } else {
            if ($field_modifier_float < 0) {
                $min_total_custom_field_modifier += $field_modifier_float;
            }
        }
    }
}

$initial_display_price = (float)$product['base_price'] + $min_total_variation_modifier + $min_total_custom_field_modifier;

$images = db_query(
    "SELECT id, filename, alt_text_pt, is_default FROM product_images WHERE product_id = :pid ORDER BY sort_order ASC",
    [':pid' => $product['id']],
    false, true
);

require_once __DIR__ . '/../../includes/product_functions.php';
$default_image = get_product_default_image($product['id']);
$default_image_url = get_asset_url('images/placeholder.png');
$default_image_id = null;

if ($default_image && !empty($default_image['filename'])) {
    $default_image_url = get_product_image_url($default_image['filename']);
    $default_image_id = $default_image['id'];
}

$gallery_images = [];
if ($images) {
    foreach ($images as $index => $img) {

        if (!empty($img['filename'])) {
            $img_url = get_product_image_url($img['filename']);
            $alt_text = !empty($img['alt_text_pt']) ? sanitize_input($img['alt_text_pt']) : sanitize_input($product['name_pt']) . ($index > 0 ? ' - Imagem ' . ($index + 1) : '');

            $gallery_images[] = [
                'id' => $img['id'],
                'url' => $img_url,
                'alt' => $alt_text,
                'filename' => $img['filename'],
                'type' => 'image',
                'is_default' => ($img['id'] === $default_image_id)
            ];
        } else {

        }
    }
} else {

    if ($images === false) {
    } else {

    }
}

require_once __DIR__ . '/../../includes/product_video_functions.php';
$videos = get_product_videos($product['id']);
$gallery_videos = [];

if ($videos) {
    foreach ($videos as $video) {
        if ($video['video_type'] === 'uploaded' && !empty($video['filename'])) {

            $video_url = get_video_url($video);
            $thumbnail_url = !empty($video['thumbnail_filename'])
                ? get_asset_url('images/video_thumbnails/' . $video['thumbnail_filename'])
                : get_asset_url('images/video_placeholder.png');

            $gallery_videos[] = [
                'id' => $video['id'],
                'url' => $video_url,
                'thumbnail_url' => $thumbnail_url,
                'alt' => sanitize_input($product['name_pt']) . ' - Vídeo',
                'type' => 'uploaded_video',
                'filename' => $video['filename']
            ];
        } elseif ($video['video_type'] === 'external' && !empty($video['video_url'])) {

            $youtube_id = extract_youtube_video_id($video['video_url']);
            if ($youtube_id) {
                $thumbnail_url = get_youtube_thumbnail_url($youtube_id);
                $embed_code = get_video_embed_code($video['video_url']);

                $gallery_videos[] = [
                    'id' => $video['id'],
                    'url' => $video['video_url'],
                    'thumbnail_url' => $thumbnail_url,
                    'alt' => sanitize_input($product['name_pt']) . ' - Vídeo',
                    'type' => 'youtube_video',
                    'youtube_id' => $youtube_id,
                    'embed_code' => $embed_code
                ];
            }
        }
    }
}

$gallery_items = array_merge($gallery_images, $gallery_videos);

if (empty($gallery_images) && !empty($gallery_videos)) {
    $default_image_url = $gallery_videos[0]['thumbnail_url'];
}

$currency_symbol = get_setting('currency_symbol', '€');
?>

<!-- File Upload Preview Styles -->
<style>
    /* File Upload Preview Styles */
    .custom-field-upload-label {
        transition: all 0.3s ease;
        overflow: hidden;
    }
    .custom-field-upload-label.border-primary {
        border-color: var(--color-primary);
    }
    .file-preview-content {
        width: 100%;
        max-width: 100%;
    }
    .file-thumbnail img {
        max-height: 4rem;
        border-radius: 0.25rem;
        object-fit: contain;
    }
    .file-name {
        max-width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    .remove-file {
        transition: color 0.2s ease;
    }
    .upload-placeholder, .file-preview {
        width: 100%;
        height: 100%;
    }
</style>

<div class="grid grid-cols-1 md:grid-cols-1 gap-6 max-w-7xl mx-auto px-4">
    <?php
        if (!is_mobile_device()) {
            require_once __DIR__ . '/../../includes/banner_display.php';
            echo display_wide_banner('mb-6');
        }
    ?>
</div>

<div class="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-7xl mx-auto px-4">
    
    <!-- Product Images -->
    <div>
        <!-- Main Image/Video Container -->
        <div class="bg-gray-800 rounded-lg overflow-hidden mb-4 aspect-square w-full" id="main-media-container">
            <!-- Default: Show Image -->
            <a href="<?= $default_image_url ?>" data-fancybox="gallery" data-caption="<?= sanitize_input($product['name_pt']) ?>" id="main-image-link">
                <img id="mainImage" src="<?= $default_image_url ?>" alt="<?= sanitize_input($product['name_pt']) ?>" class="w-full h-full object-contain p-2 cursor-zoom-in">
            </a>

            <!-- Video Container (Hidden by Default) -->
            <div id="video-container" class="w-full h-full hidden">
                <!-- Video player will be inserted here by JavaScript -->
            </div>
        </div>

        <!-- Gallery Thumbnails -->
        <?php if (count($gallery_items) > 1): ?>
            <div class="grid grid-cols-4 gap-2 w-full mb-6">
                <?php
                $first_item = true;
                foreach ($gallery_items as $index => $item):
                    $is_video = isset($item['type']) && ($item['type'] === 'uploaded_video' || $item['type'] === 'youtube_video');
                    $thumbnail_url = $is_video ? $item['thumbnail_url'] : $item['url'];
                ?>
                    <div class="bg-gray-800 rounded cursor-pointer gallery-item <?= $first_item ? 'border-2 border-primary' : '' ?>"
                         data-item-type="<?= $item['type'] ?>"
                         data-large-src="<?= $is_video ? $thumbnail_url : $item['url'] ?>"
                         <?php if ($is_video): ?>
                         data-video-url="<?= $item['url'] ?>"
                         <?php if ($item['type'] === 'youtube_video'): ?>
                         data-youtube-id="<?= $item['youtube_id'] ?>"
                         data-embed-code="<?= htmlspecialchars($item['embed_code']) ?>"
                         <?php endif; ?>
                         <?php endif; ?>>
                        <div class="relative">
                            <img src="<?= $thumbnail_url ?>" alt="<?= $item['alt'] ?>" class="w-full h-20 object-cover">
                            <?php if ($is_video): ?>
                            <div class="absolute inset-0 flex items-center justify-center">
                                <i class="ri-play-circle-fill text-white text-2xl opacity-80"></i>
                            <?php endif; ?>
                        </div>
                    </div>
                    <?php $first_item = false; ?>
                <?php endforeach; ?>
            </div>

            <!-- Hidden Gallery Links for Fancybox (Images Only) -->
            <div class="hidden">
                <?php foreach ($gallery_images as $index => $img): ?>
                    <?php if ($index > 0): ?>
                        <a href="<?= $img['url'] ?>" data-fancybox="gallery" data-caption="<?= $img['alt'] ?>"></a>
                    <?php endif; ?>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>

        <!-- Product Information Fields -->
        <?php

        require_once __DIR__ . '/../../includes/product_info_fields.php';

        $product_info_fields = get_product_info_fields($product['id']);

        if (!empty($product_info_fields) || true):
        ?>
        <div class="mb-6 bg-gray-800/50 p-4 rounded-lg">
            <h3 class="text-lg font-medium text-gray-200 mb-3">Informações Adicionais:</h3>
            <div class="flex flex-col space-y-3 text-sm text-gray-400">
                <?php

                if (empty($product_info_fields)):
                ?>
                <div class="flex items-center bg-gray-800 p-3 rounded-lg">
                    <div class="w-8 h-8 flex items-center justify-center mr-3 text-primary"><i class="ri-truck-line text-xl"></i></div>
                    <span><?= sanitize_input(get_setting('product_shipping_info', 'Envio gratuito acima de 100€')) ?></span>
                </div>
                <div class="flex items-center bg-gray-800 p-3 rounded-lg">
                    <div class="w-8 h-8 flex items-center justify-center mr-3 text-primary"><i class="ri-refresh-line text-xl"></i></div>
                    <span><?= sanitize_input(get_setting('product_return_policy', '30 dias para devolução')) ?></span>
                </div>
                <?php else: ?>
                    <?php foreach ($product_info_fields as $field): ?>
                    <div class="flex items-center bg-gray-800 p-3 rounded-lg">
                        <div class="w-8 h-8 flex items-center justify-center mr-3 text-primary"><i class="<?= sanitize_input($field['icon']) ?> text-xl"></i></div>
                        <span><?= sanitize_input($field['text']) ?></span>
                    </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>

        <?php

        if (isset($product['product_type']) && $product['product_type'] === 'digital'):

            $digital_files_placeholder = get_page_placeholder_by_slug('ficheiros-digitais');

            if ($digital_files_placeholder):

                $placeholder_pages = get_pages_by_placeholder_id($digital_files_placeholder['id']);

                if (!empty($placeholder_pages)):
        ?>
        <div class="mb-6 bg-gray-800/50 p-4 rounded-lg">
            <h3 class="text-lg font-medium text-gray-200 mb-3"><?= htmlspecialchars($digital_files_placeholder['name']) ?></h3>
            <ul class="space-y-2">
                <?php foreach ($placeholder_pages as $page): ?>
                    <li>
                        <a href="<?= get_page_url($page['slug']) ?>" class="text-gray-400 hover:text-white flex items-center" target="_blank" rel="noopener noreferrer">
                            <i class="ri-file-text-line mr-2"></i>
                            <?= htmlspecialchars($page['title']) ?>
                        </a>
                    </li>
                <?php endforeach; ?>
            </ul>
        </div>
        <?php
                endif;
            endif;
        endif;
        ?>
        <!-- End Digital Files Placeholder Section -->

        <!-- Banner Placeholder Section -->
         <?php
            
            if (!is_mobile_device()) {
                require_once __DIR__ . '/../../includes/banner_display.php';
                echo display_small_banner('mb-4');
            }
        ?>
        <!-- End Banner Placeholder Section -->

    </div>

    <!-- Product Info -->
    <div class="px-4 md:px-6 flex flex-col justify-start">
        <h1 class="text-2xl font-semibold mb-2">
            <?= sanitize_input($product['name_pt']) ?>
            <?php if (isset($product['product_type']) && $product['product_type'] === 'digital'): ?>
            <span class="inline-flex items-center ml-2 px-2 py-0.5 rounded-full text-xs font-medium bg-blue-900 text-blue-200">
                <i class="ri-download-line mr-1"></i> Produto Digital
            </span>
            <?php endif; ?>
        </h1>

        <!-- Product Categories -->
        <?php
        $product_categories = get_product_categories($product['id']);
        if (!empty($product_categories)):
        ?>
        <div class="flex flex-wrap gap-2 mb-3">
            <?php foreach ($product_categories as $category): ?>
                <a href="<?= BASE_URL . '/index.php?category_id=' . urlencode($category['id']) . '&' . get_session_id_param() ?>"
                   class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-gray-700 text-gray-300 hover:bg-primary hover:text-white transition-colors">
                    <?= sanitize_input($category['name']) ?>
                </a>
            <?php endforeach; ?>
        </div>
        <?php endif; ?>

        <!-- Optional: Add Ratings -->
        <!-- <div class="flex items-center mb-4"> ... ratings ... </div> -->

        <?php $has_options = !empty($attributes) || !empty($product_custom_fields); ?>
        <div class="text-2xl font-bold mb-2" id="product-price-display">
            <?php if ($has_options): ?>
                Desde:
            <?php endif; ?>
            <?= format_price($initial_display_price, $currency_symbol) ?>
        </div>

        <!-- VAT Rate Info -->
        <div class="text-sm text-gray-400 mb-2">
            IVA: <?php if (!empty($product['vat_description'])): ?>
                <span class="text-gray-500"><?= sanitize_input($product['vat_description']) ?> </span>
            <?php endif; ?>
            <span id="vat-rate-display"> a <?= number_format($product['vat_rate'] ?? 0, 1, ',', '.') ?>%</span>
        </div>

        <!-- SKU & Stock -->
        <div class="text-sm text-gray-400 mb-4">
            <?php if (!empty($product['sku'])): ?>
                SKU: <span id="product-sku-display"><?= sanitize_input($product['sku']) ?></span> |
                <?php if ($product['product_type'] === 'digital'): ?>
                    Disponibilidade: <span id="product-stock-display" class="font-medium text-blue-400">
                        Download Imediato
                    </span>
                <?php else: ?>
                    Disponibilidade: <span id="product-stock-display" class="font-medium <?= $product['stock'] > 0 ? 'text-green-500' : 'text-red-500' ?>">
                        <?= $product['stock'] > 0 ? 'Em Stock (' . $product['stock'] . ')' : 'Esgotado' ?>
                    </span>
                <?php endif; ?>
                <script>
                    document.addEventListener("DOMContentLoaded", () => {
                        window.isSimpleProduct = true;
                        window.simpleProductStock = <?= ($product['product_type'] === 'digital') ? 999 : (int)$product['stock'] ?>;
                    });
                </script>
            <?php else: ?>
                SKU: <span id="product-sku-display">N/D</span> | Disponibilidade: <span id="product-stock-display" class="font-medium text-gray-300">Selecione opções</span>
            <?php endif; ?>
        </div>

        <?php if (isset($product['product_type']) && $product['product_type'] === 'digital'):

            require_once __DIR__ . '/../../includes/digital_product_functions.php';
            $digital_product = get_digital_product_by_product_id($product['id']);

            if ($digital_product):

                
                $digital_file_id = $digital_product['digital_file_id'] ?? null;

                
                require_once __DIR__ . '/../../includes/digital_files_functions.php';
                $file_types = [];
                if ($digital_file_id) {
                    $file_types = get_digital_file_file_types((int)$digital_file_id);
                }
        ?>
        <div class="mb-6 bg-blue-900/30 border border-blue-800 rounded-lg p-4">
            <h3 class="text-lg font-medium text-blue-300 mb-2">Informações do Produto Digital</h3>
            <div class="space-y-2 text-sm">
                <p class="text-blue-200">
                    <i class="ri-information-line mr-1"></i> Este é um produto digital que será entregue por email após a confirmação do pagamento.
                </p>
                <p class="text-blue-200">
                    <i class="ri-time-line mr-1"></i> Disponível para download por <?= $digital_product['expiry_days'] ?> dias após a compra.
                </p>
                <p class="text-blue-200">
                    <i class="ri-download-line mr-1"></i> Limite de <?= $digital_product['download_limit'] ?> downloads.
                </p>
                <?php if (!empty($file_types)): ?>
                <div class="mt-3">
                    <p class="text-blue-300 font-medium mb-1">Inclui os seguintes formatos:</p>
                    <div class="flex flex-wrap gap-2">
                        <?php foreach ($file_types as $type): ?>
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-800 text-blue-200">
                            <?= sanitize_input($type['name']) ?> (<?= sanitize_input($type['extension']) ?>)
                        </span>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endif; ?>
        <?php endif; ?>

        <?php if (empty($product['sku'])): ?>
        <!-- Variation Selection - Moved after custom fields -->
        <div class="mb-6 bg-gray-800/50 p-4 rounded-lg">
            <h3 class="text-lg font-medium text-gray-200 mb-3">Variações:</h3>
            <form id="variation-form" class="space-y-4">
                <input type="hidden" name="product_id" value="<?= $product['id'] ?>">
                <input type="hidden" name="selected_variation_id" id="selected_variation_id" value="">

                <?php if ($attributes): ?>
                    <?php foreach ($attributes as $attr_id => $attribute): ?>
                        <div>
                            <label for="attribute-<?= $attr_id ?>" class="block text-sm font-medium text-gray-400 mb-1"><?= sanitize_input($attribute['name']) ?>:</label>
                            <select class="variation-select w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded text-white focus:border-primary focus:ring-primary focus:outline-none appearance-none"
                                    id="attribute-<?= $attr_id ?>" data-attribute-id="<?= $attr_id ?>" required>
                                <option value="" selected disabled>-- Selecione --</option>
                                <?php
                                $sorted_values = sort_attribute_values_naturally($attribute['values']);
                                ?>
                                <?php foreach ($sorted_values as $value_id => $value_name):

                                    $value_exists_in_variation = false;
                                    foreach ($variation_data as $var_details) {
                                        if (isset($var_details['options'][$attr_id]) && $var_details['options'][$attr_id] == $value_id) {
                                            $value_exists_in_variation = true;
                                            break;
                                        }
                                    }
                                    if ($value_exists_in_variation):

                                        $is_selected = ($has_single_variation_with_stock && count($variation_data) === 1 &&
                                                       isset($variation_data[$single_variation_id]['options'][$attr_id]) &&
                                                       $variation_data[$single_variation_id]['options'][$attr_id] == $value_id);
                                ?>
                                <option value="<?= $value_id ?>" <?= $is_selected ? 'selected' : '' ?>><?= sanitize_input($value_name) ?></option> <!-- Removed data-image attribute -->
                                <?php endif; ?>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    <?php endforeach; ?>
                <?php else:

                       if (count($variation_data) === 1) {
                           $single_variation_id = key($variation_data);

                           echo '<script>document.addEventListener("DOMContentLoaded", () => { window.singleVariationId = "' . $single_variation_id . '"; });</script>';
                       }
                ?>
                <?php endif; ?>

                <?php

                if (count($variation_data) === 1) {
                    $single_variation_id = key($variation_data);
                    echo '<script>document.addEventListener("DOMContentLoaded", () => {
                        window.singleVariationId = "' . $single_variation_id . '";
                        window.hasSingleVariation = true;
                        window.singleVariationStock = ' . $single_variation_stock . ';
                    });</script>';
                }
                ?>
                <div id="variation-error" class="text-red-400 text-sm" style="display: none;">Combinação indisponível.</div>
            </form>
        </div>
        <?php endif; ?>

        <!-- Line separator after description -->
        <hr class="border-gray-700 my-4">

        <div class="mb-6">
            <div class="prose prose-invert max-w-none text-gray-300 mb-4">
                <?= $product['description_pt'] ?: '<p>Sem descrição disponível.</p>'; ?>
            </div>

            <!-- Line separator after description -->
            <hr class="border-gray-700 my-4">

            <!-- Optional: Features List -->
            <!-- <div class="mb-4">
                <h3 class="font-medium mb-2 text-gray-200">Características:</h3>
                <ul class="text-gray-300 space-y-1">
                    <li class="flex items-start"> ... feature ... </li>
                </ul>
            </div> -->
        </div>

        <!-- Custom Fields Section - Moved before variation selection -->
        <?php

        if (!empty($product_custom_fields)):
        ?>
        <div class="mb-6 bg-gray-800/50 p-4 rounded-lg">
            <h3 class="text-lg font-medium text-gray-200 mb-3">Personalização:</h3>
            <p class="text-xs text-gray-500 mb-3"><span class="text-red-500">*</span> Campo de preenchimento obrigatório</p>
            <div class="space-y-4" id="custom-fields-container">
                <?php foreach ($product_custom_fields as $field): ?>
                    <?php

                    $price_modifier = $field['price_modifier_override'] !== null ? $field['price_modifier_override'] : $field['price_modifier'];
                    $price_display = $price_modifier > 0 ? '(+' . format_price($price_modifier, $currency_symbol) . ')' : '';
                    ?>
                    <div class="custom-field-item" data-field-id="<?= $field['id'] ?>" data-field-type="<?= $field['type_slug'] ?>" data-price-modifier="<?= $price_modifier ?>">
                        <label class="block text-sm font-medium text-gray-400 mb-1">
                            <?= sanitize_input($field['name']) ?> <?= $price_display ?>
                            <?= $field['is_required'] ? '<span class="text-red-500">*</span>' : '' ?>
                            <?php if (!empty($field['description'])): ?>
                                <span class="italic font-normal text-gray-500 ml-1">(<?= sanitize_input($field['description']) ?>)</span>
                            <?php endif; ?>
                        </label>

                        <?php if ($field['type_slug'] === 'custom-text'): ?>
                            <?php

                            $config = !empty($field['config_json']) ? json_decode($field['config_json'], true) : [];
                            $allowed_fonts = isset($config['allowed_fonts']) ? $config['allowed_fonts'] : [];
                            $has_fonts = !empty($allowed_fonts);
                            ?>

                            <?php if ($has_fonts): ?>
                            <?php
                            
                            $fonts = [];
                            foreach ($allowed_fonts as $font_id) {
                                $font = get_custom_field_font($font_id);
                                if ($font && $font['is_active']) {
                                    $fonts[] = $font;
                                }
                            }
                            
                            $has_single_font = count($fonts) === 1;
                            ?>
                            
                            <?php if (!$has_single_font): ?>
                            <div class="mb-2">
                                <label class="block text-xs text-gray-500 mb-1">Fonte:</label>
                                <select class="custom-field-font w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded text-white focus:border-primary focus:ring-primary focus:outline-none appearance-none"
                                        name="custom_field_font_<?= $field['id'] ?>"
                                        data-field-id="<?= $field['id'] ?>"
                                        <?= $field['is_required'] ? 'required' : '' ?>>
                                    <option value="">-- Selecione uma fonte --</option>
                                    <?php
                                    $google_fonts = [];
                                    foreach ($fonts as $font) {
                                        if (!empty($font['google_font_name'])) {
                                            $google_fonts[] = $font['google_font_name'];
                                        }
                                    }

                                    if (!empty($google_fonts)) {
                                        $google_fonts_str = implode('|', array_map('urlencode', $google_fonts));
                                        echo '<link href="https://fonts.googleapis.com/css?family=' . $google_fonts_str . '&display=swap" rel="stylesheet">';
                                    }

                                    foreach ($fonts as $font):
                                        $font_style = '';
                                        $font_family = '';
                                        if (!empty($font['google_font_name'])) {
                                            $font_family = $font['google_font_name'];
                                            $font_style = 'style="font-family: \'' . $font['google_font_name'] . '\', sans-serif;"';
                                        } elseif (!empty($font['file_path'])) {

                                            echo '<style>
                                                @font-face {
                                                    font-family: "custom-font-' . $font['id'] . '";
                                                    src: url("' . $font['file_path'] . '") format("truetype");
                                                }
                                            </style>';
                                            $font_family = 'custom-font-' . $font['id'];
                                            $font_style = 'style="font-family: \'custom-font-' . $font['id'] . '\', sans-serif;"';
                                        }
                                    ?>
                                    <option value="<?= $font['id'] ?>"
                                            data-font-name="<?= !empty($font['google_font_name']) ? $font['google_font_name'] : '' ?>"
                                            data-font-family="<?= $font_family ?>"
                                            <?= $font_style ?>>
                                        <?= sanitize_input($font['name']) ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <?php else: ?>
                            <!-- Single font: auto-select and hide dropdown -->
                            <?php
                            $single_font = $fonts[0];
                            $google_fonts = [];
                            if (!empty($single_font['google_font_name'])) {
                                $google_fonts[] = $single_font['google_font_name'];
                            }

                            if (!empty($google_fonts)) {
                                $google_fonts_str = implode('|', array_map('urlencode', $google_fonts));
                                echo '<link href="https://fonts.googleapis.com/css?family=' . $google_fonts_str . '&display=swap" rel="stylesheet">';
                            }

                            $font_style = '';
                            $font_family = '';
                            if (!empty($single_font['google_font_name'])) {
                                $font_family = $single_font['google_font_name'];
                                $font_style = 'style="font-family: \'' . $single_font['google_font_name'] . '\', sans-serif;"';
                            } elseif (!empty($single_font['file_path'])) {
                                echo '<style>
                                    @font-face {
                                        font-family: "custom-font-' . $single_font['id'] . '";
                                        src: url("' . $single_font['file_path'] . '") format("truetype");
                                    }
                                </style>';
                                $font_family = 'custom-font-' . $single_font['id'];
                                $font_style = 'style="font-family: \'custom-font-' . $single_font['id'] . '\', sans-serif;"';
                            }
                            ?>
                            <input type="hidden" 
                                   name="custom_field_font_<?= $field['id'] ?>"
                                   value="<?= $single_font['id'] ?>"
                                   data-field-id="<?= $field['id'] ?>"
                                   data-font-name="<?= !empty($single_font['google_font_name']) ? $single_font['google_font_name'] : '' ?>"
                                   data-font-family="<?= $font_family ?>"
                                   class="custom-field-font-hidden">
                            <?php endif; ?>

                                <!-- Font Preview Area -->
                                <!-- Enhanced & Larger Font Preview Area -->
                                <div class="font-preview-container mt-4 p-8 bg-gray-900 rounded-lg border-2 border-gray-700 hidden"
                                     id="font-preview-<?= $field['id'] ?>">
                                    <div class="text-sm text-gray-400 mb-3 text-center">Pré-visualização da fonte:</div>
                                    <div class="font-preview-text text-5xl text-white break-words min-h-[150px] max-h-[150px] flex items-center justify-center text-center p-4 overflow-hidden" style="line-height: 1.4;">
                                        Seu texto personalizado aqui...
                                    </div>
                                </div>
                            <?php endif; ?>

                            <textarea class="custom-field-text w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded text-white focus:border-primary focus:ring-primary focus:outline-none"
                                      name="custom_field_text_<?= $field['id'] ?>"
                                      data-field-id="<?= $field['id'] ?>"
                                      rows="3"
                                      minlength="<?= $field['min_chars'] ?>"
                                      maxlength="<?= $field['max_chars'] ?>"
                                      placeholder="Seu texto aqui..."
                                      <?= $field['is_required'] ? 'required' : '' ?>></textarea>

                            <?php if ($field['min_chars'] > 0 || $field['max_chars'] < 255): ?>
                            <div class="text-xs text-gray-500 mt-1">
                                <?php if ($field['min_chars'] > 0): ?>Mínimo: <?= $field['min_chars'] ?> caracteres.<?php endif; ?>
                                <?php if ($field['max_chars'] < 255): ?>Máximo: <?= $field['max_chars'] ?> caracteres.<?php endif; ?>
                            </div>
                            <?php endif; ?>

                        <?php elseif ($field['type_slug'] === 'file-upload'): ?>
                            <?php

                            $config = !empty($field['config_json']) ? json_decode($field['config_json'], true) : [];

                            $allowed_file_types = ['eps', 'svg', 'cdr', 'ps', 'afdesign', 'zip', 'rar', 'jpg', 'jpeg', 'png', 'bmp'];

                            if (!empty($config['custom_extensions'])) {
                                $custom_exts = explode(',', $config['custom_extensions']);
                                foreach ($custom_exts as $ext) {
                                    $ext = trim(strtolower($ext));
                                    if (!empty($ext) && !in_array($ext, $allowed_file_types)) {
                                        $allowed_file_types[] = $ext;
                                    }
                                }
                            }

                            if (isset($config['allowed_file_types']) && is_array($config['allowed_file_types'])) {
                                $allowed_file_types = $config['allowed_file_types'];
                            }

                            $max_file_size = isset($config['max_file_size']) ? $config['max_file_size'] : 10;

                            $accept_attr = [];
                            $mime_types = [
                                'eps' => 'application/postscript',
                                'ps' => 'application/postscript',
                                'svg' => 'image/svg+xml',
                                'zip' => 'application/zip',
                                'rar' => 'application/x-rar-compressed',
                                'jpg' => 'image/jpeg',
                                'jpeg' => 'image/jpeg',
                                'png' => 'image/png',
                                'bmp' => 'image/bmp',
                                'pdf' => 'application/pdf',
                                'doc' => 'application/msword',
                                'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                                'xls' => 'application/vnd.ms-excel',
                                'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                                'ppt' => 'application/vnd.ms-powerpoint',
                                'pptx' => 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
                                'txt' => 'text/plain',
                                'rtf' => 'application/rtf',
                                'gif' => 'image/gif',
                                'tiff' => 'image/tiff',
                                'tif' => 'image/tiff'
                            ];

                            foreach ($allowed_file_types as $ext) {
                                $ext = trim(strtolower($ext));
                                if (isset($mime_types[$ext])) {
                                    $accept_attr[] = $mime_types[$ext];
                                } else {

                                    $accept_attr[] = '.' . $ext;
                                }
                            }
                            $accept = implode(',', $accept_attr);
                            ?>

                            <div class="flex items-center justify-center w-full">
                                <!-- Use div instead of label to avoid label click behavior conflicts -->
                                <div class="flex flex-col items-center justify-center w-full h-32 border-2 border-gray-700 border-dashed rounded-lg cursor-pointer bg-gray-800 hover:bg-gray-700 custom-field-upload-label">
                                    <div class="flex flex-col items-center justify-center pt-5 pb-6 upload-placeholder">
                                        <i class="ri-upload-2-line text-2xl text-gray-400 mb-2"></i>
                                        <p class="mb-2 text-sm text-gray-400"><span class="font-semibold">Clique para carregar</span> ou arraste e solte</p>
                                        <p class="text-xs text-gray-500">
                                            <?= implode(', ', array_map('strtoupper', $allowed_file_types)) ?> (Máx. <?= $max_file_size ?>MB)
                                        </p>
                                    </div>
                                    <div class="flex flex-col items-center justify-center pt-5 pb-6 file-preview" style="display: none;">
                                        <div class="file-preview-content w-full flex flex-col items-center">
                                            <!-- Preview image will be inserted here for image files -->
                                            <div class="file-thumbnail mb-2" style="display: none;"></div>
                                            <div class="file-icon mb-2" style="display: none;">
                                                <i class="ri-file-line text-4xl text-primary"></i>
                                            </div>
                                            <p class="file-name text-sm text-gray-300 font-medium mb-1 text-center px-2"></p>
                                            <p class="file-size text-xs text-gray-500 mb-2"></p>
                                            <button type="button" class="remove-file text-xs text-red-400 hover:text-red-300 flex items-center">
                                                <i class="ri-delete-bin-line mr-1"></i> Remover
                                            </button>
                                        </div>
                                    </div>
                                    <!-- File input with explicit onclick to prevent event bubbling -->
                                    <input type="file" class="hidden custom-field-file" name="custom_field_file_<?= $field['id'] ?>"
                                           data-field-id="<?= $field['id'] ?>" accept="<?= $accept ?>" <?= $field['is_required'] ? 'required' : '' ?>
                                           onclick="event.stopPropagation();" />
                                </div>
                            </div>
                        <?php elseif ($field['type_slug'] === 'texto-dropdown'): ?>
                            <?php
                            
                            $dropdown_options = get_custom_field_dropdown_options($field['id'], true);
                            ?>
                            
                            <select class="custom-field-dropdown w-full px-4 py-2 bg-gray-800 border border-gray-700 rounded text-white focus:border-primary focus:ring-primary focus:outline-none appearance-none"
                                    name="custom_field_dropdown_<?= $field['id'] ?>"
                                    data-field-id="<?= $field['id'] ?>"
                                    <?= $field['is_required'] ? 'required' : '' ?>>
                                <option value="">-- Selecione uma opção --</option>
                                <?php foreach ($dropdown_options as $option): ?>
                                    <option value="<?= $option['option_value'] ?>"
                                            data-option-text="<?= sanitize_input($option['option_text']) ?>"
                                            data-price-modifier="<?= $option['price_modifier'] ?>">
                                        <?= sanitize_input($option['option_text']) ?>
                                        <?php if ($option['price_modifier'] > 0): ?>
                                            (+<?= format_price($option['price_modifier'], $currency_symbol) ?>)
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            
                            <?php if (empty($dropdown_options)): ?>
                                <div class="text-xs text-red-400 mt-1">
                                    Nenhuma opção disponível para este campo.
                                </div>
                            <?php endif; ?>
                        <?php endif; ?>

                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>

        <!-- Add to Cart Section -->
        <div class="flex flex-col space-y-3 mb-6 bg-gray-800/50 p-4 rounded-lg">
             <!-- Desktop Layout: Horizontal -->
             <div class="hidden sm:flex items-center">
                 <!-- Quantity Selector -->
                 <div class="flex items-center border border-gray-700 rounded-lg mr-4">
                     <?php if ($product['product_type'] === 'digital'): ?>
                         <!-- For digital products, always show quantity as 1 and disable buttons -->
                         <button id="detail-minus-btn" class="px-3 py-2 text-gray-400 hover:text-white disabled:opacity-50" aria-label="Diminuir quantidade" disabled>
                             <div class="w-5 h-5 flex items-center justify-center"><i class="ri-subtract-line"></i></div>
                         </button>
                         <input type="number" id="detail-quantity" value="1" min="1" max="1" class="w-12 bg-transparent border-none text-center text-white focus:outline-none" aria-label="Quantidade" readonly>
                         <button id="detail-plus-btn" class="px-3 py-2 text-gray-400 hover:text-white disabled:opacity-50" aria-label="Aumentar quantidade" disabled>
                             <div class="w-5 h-5 flex items-center justify-center"><i class="ri-add-line"></i></div>
                         </button>
                     <?php else: ?>
                         <!-- For regular products, show quantity based on stock -->
                         <button id="detail-minus-btn" class="px-3 py-2 text-gray-400 hover:text-white disabled:opacity-50" aria-label="Diminuir quantidade" <?= !empty($product['sku']) && $product['stock'] > 0 ? '' : 'disabled' ?>>
                             <div class="w-5 h-5 flex items-center justify-center"><i class="ri-subtract-line"></i></div>
                         </button>
                         <input type="number" id="detail-quantity" value="1" min="1" max="<?= !empty($product['sku']) ? $product['stock'] : 1 ?>" class="w-12 bg-transparent border-none text-center text-white focus:outline-none" aria-label="Quantidade" <?= !empty($product['sku']) && $product['stock'] > 0 ? '' : 'disabled' ?>>
                         <button id="detail-plus-btn" class="px-3 py-2 text-gray-400 hover:text-white disabled:opacity-50" aria-label="Aumentar quantidade" <?= !empty($product['sku']) && $product['stock'] > 1 ? '' : 'disabled' ?>>
                             <div class="w-5 h-5 flex items-center justify-center"><i class="ri-add-line"></i></div>
                         </button>
                     <?php endif; ?>
                 </div>
                 <!-- Add to Cart Button -->
                 <button id="addToCartDetail" class="add-to-cart-btn flex-1 bg-primary hover:bg-primary/90 text-white py-3 px-6 rounded-button font-medium whitespace-nowrap disabled:opacity-50 disabled:cursor-not-allowed"
                         data-product-id="<?= $product['id'] ?>"
                         data-product-type="<?= $product['product_type'] ?>"
                         <?php if ($product['product_type'] === 'digital'): ?>
                             <?php  ?>
                         <?php else: ?>
                             <?= !empty($product['sku']) ? '' : 'data-variation-id=""' ?>
                             <?= !empty($product['sku']) && $product['stock'] > 0 ? '' : 'disabled' ?>
                         <?php endif; ?>>
                     Adicionar ao Carrinho
                 </button>
             </div>
             
             <!-- Mobile Layout: Vertical Stack -->
             <div class="sm:hidden flex flex-col space-y-3">
                 <!-- Quantity Selector -->
                 <div class="flex items-center justify-center">
                     <div class="flex items-center border border-gray-700 rounded-lg">
                         <?php if ($product['product_type'] === 'digital'): ?>
                             <!-- For digital products, always show quantity as 1 and disable buttons -->
                             <button id="detail-minus-btn-mobile" class="px-3 py-2 text-gray-400 hover:text-white disabled:opacity-50" aria-label="Diminuir quantidade" disabled>
                                 <div class="w-5 h-5 flex items-center justify-center"><i class="ri-subtract-line"></i></div>
                             </button>
                             <input type="number" id="detail-quantity-mobile" value="1" min="1" max="1" class="w-12 bg-transparent border-none text-center text-white focus:outline-none" aria-label="Quantidade" readonly>
                             <button id="detail-plus-btn-mobile" class="px-3 py-2 text-gray-400 hover:text-white disabled:opacity-50" aria-label="Aumentar quantidade" disabled>
                                 <div class="w-5 h-5 flex items-center justify-center"><i class="ri-add-line"></i></div>
                             </button>
                         <?php else: ?>
                             <!-- For regular products, show quantity based on stock -->
                             <button id="detail-minus-btn-mobile" class="px-3 py-2 text-gray-400 hover:text-white disabled:opacity-50" aria-label="Diminuir quantidade" <?= !empty($product['sku']) && $product['stock'] > 0 ? '' : 'disabled' ?>>
                                 <div class="w-5 h-5 flex items-center justify-center"><i class="ri-subtract-line"></i></div>
                             </button>
                             <input type="number" id="detail-quantity-mobile" value="1" min="1" max="<?= !empty($product['sku']) ? $product['stock'] : 1 ?>" class="w-12 bg-transparent border-none text-center text-white focus:outline-none" aria-label="Quantidade" <?= !empty($product['sku']) && $product['stock'] > 0 ? '' : 'disabled' ?>>
                             <button id="detail-plus-btn-mobile" class="px-3 py-2 text-gray-400 hover:text-white disabled:opacity-50" aria-label="Aumentar quantidade" <?= !empty($product['sku']) && $product['stock'] > 1 ? '' : 'disabled' ?>>
                                 <div class="w-5 h-5 flex items-center justify-center"><i class="ri-add-line"></i></div>
                             </button>
                         <?php endif; ?>
                     </div>
                 </div>
                 
                 <!-- Add to Cart Button - Compact with Icon -->
                 <button id="addToCartDetailMobile" class="add-to-cart-btn w-full bg-primary hover:bg-primary/90 text-white py-3 px-4 rounded-button font-medium flex items-center justify-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
                         data-product-id="<?= $product['id'] ?>"
                         data-product-type="<?= $product['product_type'] ?>"
                         <?php if ($product['product_type'] === 'digital'): ?>
                             <?php  ?>
                         <?php else: ?>
                             <?= !empty($product['sku']) ? '' : 'data-variation-id=""' ?>
                             <?= !empty($product['sku']) && $product['stock'] > 0 ? '' : 'disabled' ?>
                         <?php endif; ?>>
                     <i class="ri-shopping-cart-line text-lg"></i>
                     <span>Adicionar ao Carrinho</span>
                 </button>
             </div>
             <!-- Request More Info Button -->
             <?php
             $product_reference = sanitize_input($product['sku'] ?? $product['name_pt']);
             $contact_message = "Solicito mais informações sobre o produto: " . sanitize_input($product['name_pt']) . " (Ref: " . $product_reference . ").";
             $contact_url = BASE_URL . '/index.php?view=contact'
                            . '&ref=' . urlencode($product_reference)
                            . '&msg=' . urlencode($contact_message)
                            . '&' . get_session_id_param();
             ?>
             <a href="<?= $contact_url ?>" class="w-full bg-gray-800 hover:bg-gray-700 text-white text-center py-3 px-6 rounded-button font-medium whitespace-nowrap">
                 Solicitar mais informações
             </a>
        </div>
        <!-- Social Media Sharing Section -->
        <div class="mb-6 bg-gray-800/50 p-4 rounded-lg">
            <h3 class="text-lg font-medium text-gray-200 mb-3">Partilhar este produto:</h3>
            <div class="flex flex-wrap gap-3">
                <?php
                
                $base_url = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
                
                $clean_url = preg_replace('/[?&]sid=[^&]*/', '', $base_url);
                
                $clean_url = preg_replace('/[?&]$/', '', $clean_url);
                $current_url = urlencode($clean_url);
                $product_title = urlencode(sanitize_input($product['name_pt']));
                $product_description = urlencode(sanitize_input(substr($product['description_pt'] ?? '', 0, 150) . '...'));
                ?>
                
                <!-- Facebook -->
                <a href="https://www.facebook.com/sharer/sharer.php?u=<?= $current_url ?>" 
                   target="_blank" rel="noopener noreferrer" 
                   class="flex items-center px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm"
                   title="Partilhar no Facebook">
                    <i class="ri-facebook-fill mr-2"></i>
                    <span class="hidden sm:inline">Facebook</span>
                </a>
                
                <!-- Pinterest -->
                <a href="https://pinterest.com/pin/create/button/?url=<?= $current_url ?>&description=<?= $product_title ?>" 
                   target="_blank" rel="noopener noreferrer" 
                   class="flex items-center px-3 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors text-sm"
                   title="Partilhar no Pinterest">
                    <i class="ri-pinterest-fill mr-2"></i>
                    <span class="hidden sm:inline">Pinterest</span>
                </a>
                
                <!-- Instagram (Note: Instagram doesn't support direct URL sharing, so this opens Instagram) -->
                <a href="https://www.instagram.com/" 
                   target="_blank" rel="noopener noreferrer" 
                   class="flex items-center px-3 py-2 bg-pink-600 hover:bg-pink-700 text-white rounded-lg transition-colors text-sm"
                   title="Abrir Instagram">
                    <i class="ri-instagram-fill mr-2"></i>
                    <span class="hidden sm:inline">Instagram</span>
                </a>
                
                <!-- WhatsApp -->
                <a href="https://wa.me/?text=<?= $product_title ?>%20<?= $current_url ?>" 
                   target="_blank" rel="noopener noreferrer" 
                   class="flex items-center px-3 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors text-sm"
                   title="Partilhar no WhatsApp">
                    <i class="ri-whatsapp-fill mr-2"></i>
                    <span class="hidden sm:inline">WhatsApp</span>
                </a>
                
                <!-- Signal (Note: Signal doesn't have web sharing, so this opens Signal website) -->
                <a href="https://signal.org/" 
                   target="_blank" rel="noopener noreferrer" 
                   class="flex items-center px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors text-sm"
                   title="Abrir Signal">
                    <i class="ri-signal-tower-fill mr-2"></i>
                    <span class="hidden sm:inline">Signal</span>
                </a>
                
                <!-- Messenger -->
                <a href="https://www.messenger.com/" 
                   target="_blank" rel="noopener noreferrer" 
                   class="flex items-center px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors text-sm"
                   title="Abrir Messenger">
                    <i class="ri-messenger-fill mr-2"></i>
                    <span class="hidden sm:inline">Messenger</span>
                </a>
                
                <!-- Copy Link -->
                <button onclick="copyProductLink()" 
                        class="flex items-center px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors text-sm"
                        title="Copiar ligação">
                    <i class="ri-link mr-2"></i>
                    <span class="hidden sm:inline">Copiar Link</span>
                </button>
            </div>
        </div>
        
        <script>
        function copyProductLink() {
            // Get current URL and remove session ID parameter
            let url = window.location.href;
            url = url.replace(/[?&]sid=[^&]*/g, '');
            // Clean up any remaining URL formatting issues
            url = url.replace(/[?&]$/, '');
            
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(url).then(() => {
                    showToast('Sucesso', 'Link copiado para a área de transferência!', 'ri-check-line', 'border-green-500');
                }).catch(() => {
                    fallbackCopyTextToClipboard(url);
                });
            } else {
                fallbackCopyTextToClipboard(url);
            }
        }
        
        function fallbackCopyTextToClipboard(text) {
            // Remove session ID from text before copying
            let cleanText = text.replace(/[?&]sid=[^&]*/g, '');
            cleanText = cleanText.replace(/[?&]$/, '');
            
            const textArea = document.createElement('textarea');
            textArea.value = cleanText;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            try {
                document.execCommand('copy');
                showToast('Sucesso', 'Link copiado para a área de transferência!', 'ri-check-line', 'border-green-500');
            } catch (err) {
                showToast('Erro', 'Não foi possível copiar o link.', 'ri-error-warning-line', 'border-red-500');
            }
            document.body.removeChild(textArea);
        }
        </script>
    </div>
</div>
<div>
    <!-- Similar Products Section -->
    <?php
    $similar_products = get_similar_products($product['id'], $product['description_pt'] ?? '', 6);
    if (!empty($similar_products)):
    ?>
    <div class="mb-6 bg-gray-800/50 p-4 rounded-lg">
        <h3 class="text-lg font-medium text-gray-200 mb-4">Também poderá ter interesse em:</h3>
        <div class="grid grid-cols-3 sm:grid-cols-4 md:grid-cols-6 gap-4">
            <?php foreach ($similar_products as $sim_prod): ?>
                <a href="<?= $sim_prod['url'] ?>" class="block group">
                    <div class="aspect-square bg-gray-800 rounded overflow-hidden mb-2">
                        <img src="<?= $sim_prod['image_url'] ?>" alt="<?= sanitize_input($sim_prod['name_pt']) ?>" class="w-full h-full object-cover group-hover:opacity-80 transition-opacity">
                    </div>
                    <p class="text-xs text-gray-300 truncate group-hover:text-primary"><?= sanitize_input($sim_prod['name_pt']) ?></p>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
    <?php endif; ?>
    <!-- End Similar Products Section -->
</div>
<div>
    <?php if (!is_mobile_device()) { ?>
        <!-- Keyword Cloud Section -->
        <?php
        $all_db_keywords = get_all_product_seo_keywords();
        $max_cloud_keywords = (int)get_setting('keyword_cloud_max_keywords', 20);
        $display_keywords = [];

        if (!empty($all_db_keywords)) {
            if (count($all_db_keywords) > $max_cloud_keywords) {
                shuffle($all_db_keywords);
                $display_keywords = array_slice($all_db_keywords, 0, $max_cloud_keywords);
            } else {
                $display_keywords = $all_db_keywords;
            }
        }

        if (!empty($display_keywords)):
        ?>
        <div class="mb-6 bg-gray-800/50 p-4 rounded-lg">
            <h3 class="text-lg font-medium text-gray-200 mb-4">Nuvem de Palavras-chave:</h3>
            <div id="keyword-cloud-canvas-container" class="w-full h-64 md:h-80 relative">
                <!-- Canvas for Three.js will be inserted here by JavaScript -->
            </div>
            <div id="keyword-cloud-fallback" class="flex flex-wrap gap-2 mt-4" style="display: none;">
                <?php foreach ($display_keywords as $keyword): ?>
                    <a href="index.php?page=search&q=<?= urlencode(sanitize_input($keyword)) ?>&<?= get_session_id_param() ?>"
                        class="inline-block bg-gray-700 hover:bg-primary text-gray-300 hover:text-white text-xs font-medium px-3 py-1 rounded-full transition-colors">
                        <?= sanitize_input($keyword) ?>
                    </a>
                <?php endforeach; ?>
            </div>
        </div>
        <?php endif; ?>
        <!-- End Keyword Cloud Section -->  
    <?php } ?>
</div> 

<!-- JavaScript data for variations -->
<script>
    // Pass necessary data to JavaScript
    window.productVariations = <?= json_encode($variation_data, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK) ?>;
    window.attributeValueModifiers = <?= json_encode($attribute_value_modifiers, JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK) ?>;
    // Removed window.attributeValueImages as it's no longer populated
    window.productBasePrice = <?= $product['base_price'] ?>;
    window.currencySymbol = '<?= $currency_symbol ?>';
    // Ensure these are available if not already globally defined in footer
    window.eshopSessionId = '<?= $current_session_id ?>';
    window.eshopSessionParam = 'sid';
    window.eshopBaseUrl = '<?= BASE_URL ?>';
    window.productHasOptions = <?= $has_options ? 'true' : 'false' ?>; // Flag for JS price formatting
    window.minPhpVariationModifier = <?= $min_total_variation_modifier ?>; // Pass the PHP calculated min variation modifier

    // Custom fields data
    window.customFields = <?= json_encode(array_map(function($field) {
        $price_modifier = $field['price_modifier_override'] !== null ? $field['price_modifier_override'] : $field['price_modifier'];
        return [
            'id' => $field['id'],
            'name' => $field['name'],
            'type' => $field['type_slug'],
            'price_modifier' => $price_modifier,
            'is_required' => $field['is_required'] == 1
        ];
    }, $product_custom_fields ?? []), JSON_UNESCAPED_UNICODE | JSON_NUMERIC_CHECK) ?>;

    // Debug output
</script>

<!-- Add File Upload Preview JS -->
<script src="<?= BASE_URL ?>/public/assets/js/file-upload-preview.js"></script>

<!-- Add Product Detail Specific JS -->
<script>
document.addEventListener('DOMContentLoaded', () => {
    const variationSelects = document.querySelectorAll('.variation-select');
    const priceDisplay = document.getElementById('product-price-display');
    const skuDisplay = document.getElementById('product-sku-display');
    const stockDisplay = document.getElementById('product-stock-display');
    const addToCartBtn = document.getElementById('addToCartDetail'); // Keep reference if needed elsewhere, but listener removed below
    const addToCartBtnMobile = document.getElementById('addToCartDetailMobile'); // Mobile button reference
    const quantityInput = document.getElementById('detail-quantity');
    const minusBtn = document.getElementById('detail-minus-btn');
    const plusBtn = document.getElementById('detail-plus-btn');

    // Video Gallery Elements
    const mainMediaContainer = document.getElementById('main-media-container');
    const mainImageLink = document.getElementById('main-image-link');
    const mainImage = document.getElementById('mainImage');
    const videoContainer = document.getElementById('video-container');
    const variationError = document.getElementById('variation-error');
    const selectedVariationInput = document.getElementById('selected_variation_id');

    // Check if this is a simple product or digital product
    const isSimpleProduct = window.isSimpleProduct || false;
    const isDigitalProduct = <?= isset($product['product_type']) && $product['product_type'] === 'digital' ? 'true' : 'false' ?>;
    const simpleProductStock = window.simpleProductStock || 0;

    function formatPriceJS(amount) {
        // Simple formatting, prefix handled separately
        return (window.currencySymbol || '€') + parseFloat(amount).toFixed(2).replace('.', ',');
    }

    function areAllOptionsSelected() {
        // 1. Check variation selects
        for (const select of variationSelects) {
            if (!select.value) {
                return false; // Required variation not selected
            }
        }

        // 2. Check required custom fields
        const customFieldItems = document.querySelectorAll('#custom-fields-container .custom-field-item');
        for (const fieldItem of customFieldItems) {
            const fieldData = window.customFields.find(f => f.id == fieldItem.dataset.fieldId);
            if (fieldData && fieldData.is_required) {
                const input = fieldItem.querySelector('textarea, input[type="file"], select');
                if (!input) continue;

                if (input.type === 'file') {
                    if (!input.files || input.files.length === 0) {
                       return false; // Required file not selected
                    }
                } else { // select or textarea
                    // Also check font select if it's required for a text field
                    const fontSelect = fieldItem.querySelector('.custom-field-font');
                    if (fontSelect && fontSelect.required && !fontSelect.value) {
                        return false; // Required font not selected
                    }
                    // Check the main input value
                    if (!input.value || input.value.trim() === '') {
                       return false; // Required text/select is empty
                    }
                }
            }
        }

        // If we passed all checks
        return true;
    }

    // If it's a simple product or digital product, handle it differently
    if (isSimpleProduct || isDigitalProduct) {
        // For digital products, always enable the button regardless of stock
        // Update both desktop and mobile buttons
        [addToCartBtn, addToCartBtnMobile].forEach(btn => {
            if (btn) {
                if (isDigitalProduct) {
                    // Digital products are always available
                    btn.disabled = false;
                    btn.classList.remove('disabled:opacity-50', 'disabled:cursor-not-allowed');
                } else {
                    // Regular simple products depend on stock
                    btn.disabled = simpleProductStock <= 0;
                    if (simpleProductStock <= 0) {
                        btn.classList.add('disabled:opacity-50', 'disabled:cursor-not-allowed');
                    } else {
                        btn.classList.remove('disabled:opacity-50', 'disabled:cursor-not-allowed');
                    }
                }
            }
        });

        // Set quantity input max value
        if (quantityInput) {
            if (isDigitalProduct) {
                // Digital products are limited to 1 item
                quantityInput.max = 1;
                quantityInput.value = 1;
                quantityInput.disabled = false;
            } else {
                // Regular simple products depend on stock
                quantityInput.max = Math.max(1, simpleProductStock);
                quantityInput.disabled = simpleProductStock <= 0;

                // Ensure quantity is within valid range
                if (parseInt(quantityInput.value) > simpleProductStock) {
                    quantityInput.value = Math.max(1, simpleProductStock);
                }
            }
        }
    }

    function updateVariationDetails() {
        const isSimpleProduct = window.isSimpleProduct || false;
        const isDigitalProduct = <?= isset($product['product_type']) && $product['product_type'] === 'digital' ? 'true' : 'false' ?>;
        const simpleProductStock = window.simpleProductStock || 0;
        const priceDisplay = document.getElementById('product-price-display');
        const stockDisplay = document.getElementById('product-stock-display');
        const addToCartBtn = document.getElementById('addToCartDetail');
        const quantityInput = document.getElementById('detail-quantity');

        if (isSimpleProduct && !isDigitalProduct) {
            let displayPrice = window.productBasePrice;
            let customFieldsPriceModifier = 0;
            const customFieldItems = document.querySelectorAll('#custom-fields-container .custom-field-item');
            let allRequiredCustomFieldsSelected = true;

            customFieldItems.forEach(fieldItem => {
                const fieldInput = fieldItem.querySelector('textarea, input[type="file"], select');
                const fieldData = window.customFields.find(f => f.id == fieldItem.dataset.fieldId);

                if (fieldInput) {
                    const hasValue = (fieldInput.type === 'file')
                                     ? (fieldInput.files && fieldInput.files.length > 0)
                                     : (fieldInput.value && fieldInput.value.trim() !== '');
                    const fontSelect = fieldItem.querySelector('.custom-field-font');
                    const hasFontValue = fontSelect ? (fontSelect.value && fontSelect.value.trim() !== '') : true;

                    if (hasValue && hasFontValue) {
                        customFieldsPriceModifier += parseFloat(fieldItem.dataset.priceModifier || 0);
                    }

                    if (fieldData && fieldData.is_required) {
                        if (fieldInput.type === 'file') {
                            if (!fieldInput.files || fieldInput.files.length === 0) {
                               allRequiredCustomFieldsSelected = false;
                            }
                        } else {
                            const fontSelectRequired = fieldItem.querySelector('.custom-field-font');
                            if (fontSelectRequired && fontSelectRequired.required && !fontSelectRequired.value) {
                                allRequiredCustomFieldsSelected = false;
                            }
                            if (!fieldInput.value || fieldInput.value.trim() === '') {
                               allRequiredCustomFieldsSelected = false;
                            }
                        }
                    }
                }
            });
            displayPrice += customFieldsPriceModifier;

            let displayPrefix = "";
            if (window.productHasOptions && !allRequiredCustomFieldsSelected) {
                displayPrefix = "Desde: ";
            }
            if(priceDisplay) priceDisplay.textContent = displayPrefix + formatPriceJS(displayPrice);

            // Update both desktop and mobile buttons
            [addToCartBtn, addToCartBtnMobile].forEach(btn => {
                if (btn) {
                    btn.disabled = simpleProductStock <= 0 || !allRequiredCustomFieldsSelected;
                    if (simpleProductStock <= 0 || !allRequiredCustomFieldsSelected) {
                        btn.classList.add('disabled:opacity-50', 'disabled:cursor-not-allowed');
                    } else {
                        btn.classList.remove('disabled:opacity-50', 'disabled:cursor-not-allowed');
                    }
                }
            });
            if(stockDisplay){
              if(simpleProductStock > 0){
                stockDisplay.textContent = `Em Stock (${simpleProductStock})`;
                stockDisplay.className = 'font-medium text-green-500';
              } else {
                stockDisplay.textContent = 'Esgotado';
                stockDisplay.className = 'font-medium text-red-500';
              }
            }
            updateQuantityButtonStates();
            return;
        } else if (isDigitalProduct) {
            let displayPrice = window.productBasePrice;
            let customFieldsPriceModifier = 0;
            const customFieldItems = document.querySelectorAll('#custom-fields-container .custom-field-item');
            let allRequiredCustomFieldsSelectedDigital = true;

            customFieldItems.forEach(fieldItem => {
                const fieldInput = fieldItem.querySelector('textarea, input[type="file"], select');
                const fieldData = window.customFields.find(f => f.id == fieldItem.dataset.fieldId);

                if (fieldInput) {
                    const hasValue = (fieldInput.type === 'file')
                                     ? (fieldInput.files && fieldInput.files.length > 0)
                                     : (fieldInput.value && fieldInput.value.trim() !== '');
                    const fontSelect = fieldItem.querySelector('.custom-field-font');
                    const hasFontValue = fontSelect ? (fontSelect.value && fontSelect.value.trim() !== '') : true;

                    if (hasValue && hasFontValue) {
                        customFieldsPriceModifier += parseFloat(fieldItem.dataset.priceModifier || 0);
                    }

                    if (fieldData && fieldData.is_required) {
                        if (fieldInput.type === 'file' && (!fieldInput.files || fieldInput.files.length === 0)) {
                             allRequiredCustomFieldsSelectedDigital = false;
                        } else if (fieldInput.type !== 'file' && (!fieldInput.value || fieldInput.value.trim() === '')) {
                             allRequiredCustomFieldsSelectedDigital = false;
                        }
                         const fontSelectRequired = fieldItem.querySelector('.custom-field-font');
                         if (fontSelectRequired && fontSelectRequired.required && !fontSelectRequired.value) {
                                allRequiredCustomFieldsSelectedDigital = false;
                         }
                    }
                }
            });
            displayPrice += customFieldsPriceModifier;

            let displayPrefixDigital = "";
            if (window.productHasOptions && !allRequiredCustomFieldsSelectedDigital) {
                displayPrefixDigital = "Desde: ";
            }
            if(priceDisplay) priceDisplay.textContent = displayPrefixDigital + formatPriceJS(displayPrice);

            // Update both desktop and mobile buttons
            [addToCartBtn, addToCartBtnMobile].forEach(btn => {
                if (btn) {
                    btn.disabled = !allRequiredCustomFieldsSelectedDigital;
                    if (!allRequiredCustomFieldsSelectedDigital) {
                        btn.classList.add('disabled:opacity-50', 'disabled:cursor-not-allowed');
                    } else {
                        btn.classList.remove('disabled:opacity-50', 'disabled:cursor-not-allowed');
                    }
                }
            });
            if(stockDisplay) { // Digital products always show "Download Imediato"
                stockDisplay.innerHTML = `Download Imediato`; // Use innerHTML if it contains icons/spans
                stockDisplay.className = 'font-medium text-blue-400';
            }
            updateQuantityButtonStates();
            return;
        }

        // Continue with existing variation logic if not a simple or digital product
        const selectedOptions = {};
        let allSelected = true;
        variationSelects.forEach(select => {
            const attrId = select.getAttribute('data-attribute-id');
            if (select.value) {
                selectedOptions[attrId] = select.value;
            } else {
                allSelected = false;
            }
        });

        let matchedVariationId = null;
        let matchedVariation = null;
        let detailAddToCartBtn = document.getElementById('addToCartDetail'); // Get button reference inside function
        let detailAddToCartBtnMobile = document.getElementById('addToCartDetailMobile'); // Mobile button reference inside function

        // Special case: If we have variations but no selects are shown (all variations have same attribute values)
        // or if we have only one attribute with multiple values
        if (variationSelects.length === 0 && Object.keys(window.productVariations).length > 0) {
            // Just pick the first variation with stock
            for (const varId in window.productVariations) {
                const variation = window.productVariations[varId];
                if (parseInt(variation.stock) > 0) {
                    matchedVariationId = varId;
                    matchedVariation = variation;
                    allSelected = true;
                    break;
                }
            }
            // If no variation with stock found, just pick the first one
            if (!matchedVariation) {
                const firstVarId = Object.keys(window.productVariations)[0];
                matchedVariationId = firstVarId;
                matchedVariation = window.productVariations[firstVarId];
                allSelected = true;
            }
        } else if (allSelected && Object.keys(selectedOptions).length === variationSelects.length) {
            // Normal case: user has selected all options
            for (const varId in window.productVariations) {
                const variation = window.productVariations[varId];
                let optionsMatch = true;
                // Check if all selected options match the variation's options
                for (const attrId in selectedOptions) {
                    if (!variation.options[attrId] || variation.options[attrId] != selectedOptions[attrId]) {
                        optionsMatch = false;
                        break;
                    }
                }
                // Ensure the number of options matches exactly
                if (optionsMatch && Object.keys(variation.options).length === Object.keys(selectedOptions).length) {
                    matchedVariationId = varId;
                    matchedVariation = variation;
                    break;
                }
            }
        } else if (window.singleVariationId && (variationSelects.length === 0 || Object.keys(window.productVariations).length === 1)) {
            // Handle both cases:
            // 1. No variation selects but has a single variation
            // 2. Has variation selects but only one variation exists (pre-select it)
            matchedVariationId = window.singleVariationId;
            matchedVariation = window.productVariations[matchedVariationId];
            allSelected = true; // Treat as selected

            // If we have a single variation with attributes, pre-select the options
            if (variationSelects.length > 0 && Object.keys(window.productVariations).length === 1) {
                const variation = window.productVariations[window.singleVariationId];
                // Pre-select the options in the dropdowns
                variationSelects.forEach(select => {
                    const attrId = select.getAttribute('data-attribute-id');
                    if (variation.options[attrId]) {
                        select.value = variation.options[attrId];
                        selectedOptions[attrId] = variation.options[attrId];
                    }
                });
            }
        }

        if (matchedVariation) {
            if (variationError) variationError.style.display = 'none';
            if (selectedVariationInput) selectedVariationInput.value = matchedVariationId;
            // Set variation ID on both buttons
            if (detailAddToCartBtn) detailAddToCartBtn.setAttribute('data-variation-id', matchedVariationId);
            if (detailAddToCartBtnMobile) detailAddToCartBtnMobile.setAttribute('data-variation-id', matchedVariationId);

            // --- Price Calculation and Display Logic ---
            let displayPrice = window.productBasePrice;
            let displayPrefix = "";
            let variationPriceModifier = 0;
            let customFieldsPriceModifier = 0;

            // 1. Calculate Custom Field Modifier (always applies if fields are filled)
            const customFieldItems = document.querySelectorAll('#custom-fields-container .custom-field-item');
            customFieldItems.forEach(fieldItem => {
                const fieldInput = fieldItem.querySelector('textarea, input[type="file"], select');
                if (fieldInput) {
                    const hasValue = (fieldInput.type === 'file')
                                     ? (fieldInput.files && fieldInput.files.length > 0)
                                     : (fieldInput.value && fieldInput.value.trim() !== '');
                    const fontSelect = fieldItem.querySelector('.custom-field-font');
                    const hasFontValue = fontSelect ? (fontSelect.value && fontSelect.value.trim() !== '') : true;

                    if (hasValue && hasFontValue) {
                        customFieldsPriceModifier += parseFloat(fieldItem.dataset.priceModifier || 0);
                    }
                }
            });

            // 2. Calculate Variation Modifier (only if a variation is matched)
            if (matchedVariation) { // This check ensures 'selectedOptions' are relevant to a *matched* variation
                if (typeof matchedVariation.price_modifier_override !== 'undefined' && matchedVariation.price_modifier_override !== null) {
                    variationPriceModifier = parseFloat(matchedVariation.price_modifier_override);
                } else {
                    // Sum attribute modifiers only for the options of the *matched* variation
                    for (const attrId in matchedVariation.options) {
                        const valueId = matchedVariation.options[attrId];
                        // Ensure this option was actually selected by the user if there are dropdowns
                        // For single variations or no-dropdown variations, selectedOptions might be empty but matchedVariation.options is not.
                        if (variationSelects.length === 0 || selectedOptions[attrId] == valueId) {
                           variationPriceModifier += window.attributeValueModifiers[valueId] || 0;
                        }
                    }
                }
            }

            // 3. Calculate Total Price
            displayPrice = window.productBasePrice + variationPriceModifier + customFieldsPriceModifier;

            // 4. Determine Prefix based on all *required* options
            const allRequiredOptionsSelected = areAllOptionsSelected();
            if (window.productHasOptions) {
                if (allRequiredOptionsSelected) {
                    displayPrefix = ""; // No prefix for final price
                } else {
                    displayPrefix = "Desde: ";
                }
            } else {
                // Simple product, no options, no prefix
                displayPrefix = "";
            }

            priceDisplay.textContent = displayPrefix + formatPriceJS(displayPrice);
            // --- End Price Calculation ---

            // Update SKU and Stock (Based only on variation selection)
            skuDisplay.textContent = matchedVariation.sku || 'N/D';
            const stock = parseInt(matchedVariation.stock);
            if (stock > 0) {
                stockDisplay.textContent = `Em Stock (${stock} disponíveis)`;
                stockDisplay.className = 'font-medium text-green-400';
                quantityInput.max = stock;
                quantityInput.disabled = false;
                // Enable both buttons when stock is available
                [detailAddToCartBtn, detailAddToCartBtnMobile].forEach(btn => {
                    if (btn) {
                        btn.disabled = false;
                        btn.classList.remove('disabled:opacity-50', 'disabled:cursor-not-allowed');
                    }
                });
            } else {
                stockDisplay.textContent = 'Esgotado';
                stockDisplay.className = 'font-medium text-red-400';
                quantityInput.max = 0;
                quantityInput.value = 0;
                quantityInput.disabled = true;
                // Disable both buttons when out of stock
                [detailAddToCartBtn, detailAddToCartBtnMobile].forEach(btn => {
                    if (btn) {
                        btn.disabled = true;
                        btn.classList.add('disabled:opacity-50', 'disabled:cursor-not-allowed');
                    }
                });
            }

              // Removed logic to update main image based on selected value image, as it's no longer available
              // Ensure default image and first thumbnail highlight remain correct
              if (mainImage) {
                  mainImage.src = '<?= $default_image_url ?>';
                  document.querySelectorAll('.thumbnail-item').forEach((t, idx) => {
                      if (idx === 0) t.classList.add('border-primary', 'border-2');
                      else t.classList.remove('border-primary', 'border-2');
                  });
              }

         } else {
            selectedVariationInput.value = '';
            // Remove variation ID from both buttons
            if (detailAddToCartBtn) detailAddToCartBtn.removeAttribute('data-variation-id');
            if (detailAddToCartBtnMobile) detailAddToCartBtnMobile.removeAttribute('data-variation-id');

            // Calculate custom field modifier even when no variation is matched
            let currentCustomFieldsPriceModifier = 0;
            document.querySelectorAll('#custom-fields-container .custom-field-item').forEach(fieldItem => {
                const fieldInput = fieldItem.querySelector('textarea, input[type="file"], select');
                if (fieldInput) {
                    const hasValue = (fieldInput.type === 'file')
                                     ? (fieldInput.files && fieldInput.files.length > 0)
                                     : (fieldInput.value && fieldInput.value.trim() !== '');
                    const fontSelect = fieldItem.querySelector('.custom-field-font');
                    const hasFontValue = fontSelect ? (fontSelect.value && fontSelect.value.trim() !== '') : true;
                    if (hasValue && hasFontValue) {
                        currentCustomFieldsPriceModifier += parseFloat(fieldItem.dataset.priceModifier || 0);
                    }
                }
            });

            // When no variation is matched (e.g., on initial load with selectable options),
            // use the PHP-calculated minimum variation modifier for the "Desde:" price.
            const initialVariationModifier = window.minPhpVariationModifier || 0;
            const intermediatePrice = window.productBasePrice + initialVariationModifier + currentCustomFieldsPriceModifier;
            const basePricePrefix = window.productHasOptions ? "Desde: " : ""; // Prefix still applies if options exist but variation not chosen
            priceDisplay.textContent = basePricePrefix + formatPriceJS(intermediatePrice);
            skuDisplay.textContent = 'N/D';
            stockDisplay.textContent = 'Selecione opções';
            stockDisplay.className = 'font-medium text-gray-300';
            quantityInput.max = 1;
            quantityInput.value = 1;
            quantityInput.disabled = true;
            // Disable both buttons when no variation is matched
            [detailAddToCartBtn, detailAddToCartBtnMobile].forEach(btn => {
                if (btn) {
                    btn.disabled = true;
                    btn.classList.add('disabled:opacity-50', 'disabled:cursor-not-allowed');
                }
            });
            if (variationError) {
                if (allSelected && variationSelects.length > 0) { // Only show error if all dropdowns are selected but no match found
                    variationError.style.display = 'block';
                } else {
                    variationError.style.display = 'none';
                }
            }
             // Revert image to default
             if (mainImage) {
                 mainImage.src = '<?= $default_image_url ?>';
                  // Highlight the first thumbnail again
                 document.querySelectorAll('.thumbnail-item').forEach((t, idx) => {
                     if (idx === 0) t.classList.add('border-primary', 'border-2');
                     else t.classList.remove('border-primary', 'border-2');
                 });
             }
        }
         // Update quantity button states based on current value and max stock
         updateQuantityButtonStates();
    }

     function updateQuantityButtonStates() {
        const currentValue = parseInt(quantityInput.value);
        const maxStock = parseInt(quantityInput.max);
        
        // Get mobile elements
        const minusBtnMobile = document.getElementById('detail-minus-btn-mobile');
        const plusBtnMobile = document.getElementById('detail-plus-btn-mobile');
        const quantityInputMobile = document.getElementById('detail-quantity-mobile');

        // For digital products, always disable plus button (limited to 1)
        if (isDigitalProduct) {
            // Desktop buttons
            if (minusBtn) minusBtn.disabled = true; // Always disable minus for digital (always 1)
            if (plusBtn) plusBtn.disabled = true;  // Always disable plus for digital (always 1)
            // Mobile buttons
            if (minusBtnMobile) minusBtnMobile.disabled = true;
            if (plusBtnMobile) plusBtnMobile.disabled = true;
        } else {
            // Desktop buttons
            if (minusBtn) minusBtn.disabled = currentValue <= 1 || quantityInput.disabled;
            if (plusBtn) plusBtn.disabled = currentValue >= maxStock || quantityInput.disabled;
            // Mobile buttons
            if (minusBtnMobile) minusBtnMobile.disabled = currentValue <= 1 || (quantityInputMobile ? quantityInputMobile.disabled : quantityInput.disabled);
            if (plusBtnMobile) plusBtnMobile.disabled = currentValue >= maxStock || (quantityInputMobile ? quantityInputMobile.disabled : quantityInput.disabled);
        }
        
        // Sync mobile input properties with desktop input
        if (quantityInputMobile && quantityInput) {
            quantityInputMobile.max = quantityInput.max;
            quantityInputMobile.min = quantityInput.min;
            quantityInputMobile.disabled = quantityInput.disabled;
            quantityInputMobile.value = quantityInput.value;
        }
    }

    variationSelects.forEach(select => {
        select.addEventListener('change', updateVariationDetails);
    });

    // Add listeners to custom fields as well
    const customFieldInputs = document.querySelectorAll('#custom-fields-container textarea, #custom-fields-container input[type="file"], #custom-fields-container select');
    customFieldInputs.forEach(input => {
        const eventType = (input.tagName === 'TEXTAREA') ? 'input' : 'change';
        input.addEventListener(eventType, updateVariationDetails);
    });

     // Quantity buttons specific to detail page - Desktop
    if (minusBtn && plusBtn && quantityInput) {
        minusBtn.addEventListener('click', () => {
            let value = parseInt(quantityInput.value);
            if (value > 1) {
                quantityInput.value = value - 1;
                // Sync with mobile input
                const mobileQuantityInput = document.getElementById('detail-quantity-mobile');
                if (mobileQuantityInput) mobileQuantityInput.value = quantityInput.value;
                updateQuantityButtonStates();
            }
        });
        plusBtn.addEventListener('click', () => {
            let value = parseInt(quantityInput.value);
            let max = parseInt(quantityInput.max);
            if (value < max) {
                quantityInput.value = value + 1;
                // Sync with mobile input
                const mobileQuantityInput = document.getElementById('detail-quantity-mobile');
                if (mobileQuantityInput) mobileQuantityInput.value = quantityInput.value;
                 updateQuantityButtonStates();
            }
        });
         quantityInput.addEventListener('change', () => { // Handle manual input change
            let value = parseInt(quantityInput.value);
            let min = parseInt(quantityInput.min);
            let max = parseInt(quantityInput.max);
             if (isNaN(value) || value < min) {
                 quantityInput.value = min;
             } else if (value > max) {
                 quantityInput.value = max;
             }
             // Sync with mobile input
             const mobileQuantityInput = document.getElementById('detail-quantity-mobile');
             if (mobileQuantityInput) mobileQuantityInput.value = quantityInput.value;
             updateQuantityButtonStates();
        });
    }

    // Quantity buttons specific to detail page - Mobile
    const minusBtnMobile = document.getElementById('detail-minus-btn-mobile');
    const plusBtnMobile = document.getElementById('detail-plus-btn-mobile');
    const quantityInputMobile = document.getElementById('detail-quantity-mobile');
    
    if (minusBtnMobile && plusBtnMobile && quantityInputMobile) {
        minusBtnMobile.addEventListener('click', () => {
            let value = parseInt(quantityInputMobile.value);
            if (value > 1) {
                quantityInputMobile.value = value - 1;
                // Sync with desktop input
                if (quantityInput) quantityInput.value = quantityInputMobile.value;
                updateQuantityButtonStates();
            }
        });
        plusBtnMobile.addEventListener('click', () => {
            let value = parseInt(quantityInputMobile.value);
            let max = parseInt(quantityInputMobile.max);
            if (value < max) {
                quantityInputMobile.value = value + 1;
                // Sync with desktop input
                if (quantityInput) quantityInput.value = quantityInputMobile.value;
                updateQuantityButtonStates();
            }
        });
        quantityInputMobile.addEventListener('change', () => { // Handle manual input change
            let value = parseInt(quantityInputMobile.value);
            let min = parseInt(quantityInputMobile.min);
            let max = parseInt(quantityInputMobile.max);
            if (isNaN(value) || value < min) {
                quantityInputMobile.value = min;
            } else if (value > max) {
                quantityInputMobile.value = max;
            }
            // Sync with desktop input
            if (quantityInput) quantityInput.value = quantityInputMobile.value;
            updateQuantityButtonStates();
        });
    }

    // For simple products and digital products, we don't need to call updateVariationDetails
    // For products with variations, call it to initialize the UI
    if (!isSimpleProduct && !isDigitalProduct) {
        updateVariationDetails();
    }

     // Gallery Item Click Handler (Images and Videos)
     const galleryItems = document.querySelectorAll('.gallery-item');

     // Function to show video
     function showVideo(item) {
         // Hide the image link and show the video container
         mainImageLink.classList.add('hidden');
         videoContainer.classList.remove('hidden');

         // Clear previous video content
         videoContainer.innerHTML = '';

         const itemType = item.getAttribute('data-item-type');

         if (itemType === 'uploaded_video') {
             // Create HTML5 video player for uploaded videos
             const videoUrl = item.getAttribute('data-video-url');
             const videoElement = document.createElement('video');
             videoElement.src = videoUrl;
             videoElement.className = 'w-full h-full object-contain';
             videoElement.controls = true;
             videoElement.autoplay = false;
             videoElement.playsInline = true;

             videoContainer.appendChild(videoElement);
         } else if (itemType === 'youtube_video') {
             // Create iframe for YouTube videos
             const embedCode = item.getAttribute('data-embed-code');
             videoContainer.innerHTML = embedCode;

             // Make sure the iframe fills the container
             const iframe = videoContainer.querySelector('iframe');
             if (iframe) {
                 iframe.className = 'w-full h-full';
             }
         }
     }

     // Function to show image
     function showImage(item) {
         // Hide video container and show image
         videoContainer.classList.add('hidden');
         mainImageLink.classList.remove('hidden');

         // Update image source
         const newSrc = item.getAttribute('data-large-src');
         const imgAlt = item.querySelector('img').alt || '';

         mainImage.src = newSrc;
         mainImage.alt = imgAlt;

         // Update lightbox link
         mainImageLink.href = newSrc;
         mainImageLink.setAttribute('data-caption', imgAlt);
     }

     galleryItems.forEach(item => {
         // Check if listener already added
         if (!item.hasAttribute('data-listener-added')) {
             item.addEventListener('click', function() {
                 // Update active state
                 galleryItems.forEach(t => t.classList.remove('border-primary', 'border-2'));
                 this.classList.add('border-primary', 'border-2');

                 const itemType = this.getAttribute('data-item-type');

                 if (itemType === 'image') {
                     showImage(this);
                 } else if (itemType === 'uploaded_video' || itemType === 'youtube_video') {
                     showVideo(this);
                 }
             });
             item.setAttribute('data-listener-added', 'true'); // Mark as added
         }
     });

     // Initialize Fancybox for version 4
     document.addEventListener('DOMContentLoaded', function() {
         if (typeof Fancybox !== 'undefined') {
             Fancybox.bind('[data-fancybox="gallery"]', {
                 // Fancybox v4 options
                 buttons: [
                     "zoom",
                     "slideShow",
                     "fullScreen",
                     "thumbs",
                     "close"
                 ],
                 loop: true,
                 animationEffect: "fade",
                 transitionEffect: "fade",
                 // Ensure images are preloaded
                 preload: true
             });
         }
     });

     // REMOVED the specific listener for addToCartDetail button
     // The generic listener in main.js will handle clicks on elements with class 'add-to-cart-btn'

     // Handle font selection for custom text fields and update ONLY the preview
     const fontSelects = document.querySelectorAll('.custom-field-font');
     fontSelects.forEach(select => {
         select.addEventListener('change', function() {
             const fieldId = this.dataset.fieldId;
             const fieldContainer = this.closest('.custom-field-item'); // Needed for textarea lookup
             const textArea = fieldContainer.querySelector('.custom-field-text'); // Find corresponding textarea
             const previewContainer = document.getElementById(`font-preview-${fieldId}`);
             const previewTextElement = previewContainer ? previewContainer.querySelector('.font-preview-text') : null;
             const selectedOption = this.options[this.selectedIndex];

             // Reset ONLY preview font
             if (previewTextElement) previewTextElement.style.fontFamily = '';

             if (selectedOption && selectedOption.value && previewContainer) {
                 const fontFamily = selectedOption.getAttribute('data-font-family');

                 if (fontFamily) {
                     const finalFontFamily = `'${fontFamily}', sans-serif`;
                     // Apply font ONLY to the preview element
                     if (previewTextElement) previewTextElement.style.fontFamily = finalFontFamily;
                     previewContainer.classList.remove('hidden');
                 } else {
                     // Font family not found, hide preview
                     previewContainer.classList.add('hidden');
                 }
             } else if (previewContainer) {
                 // No font selected or invalid, hide preview
                 previewContainer.classList.add('hidden');
             }

             // Update preview text with textarea content whenever font changes too
             if (previewTextElement && textArea) {
                  const currentText = textArea.value || textArea.placeholder || 'Digite seu texto personalizado aqui...';
                  previewTextElement.textContent = currentText || 'Digite seu texto personalizado aqui...'; // Ensure it's never empty
                  // Auto-adjust font size when font changes
                  adjustFontSizeForPreview(previewTextElement, currentText);
             }
         });

         // Add listener to the corresponding textarea to update preview text in real-time
         const fieldContainer = select.closest('.custom-field-item');
         const textArea = fieldContainer.querySelector('.custom-field-text');
         if (textArea) {
             textArea.addEventListener('input', function() {
                 const fieldId = this.dataset.fieldId;
                 const previewTextElement = document.querySelector(`#font-preview-${fieldId} .font-preview-text`);
                 if (previewTextElement) {
                     // Use value, fallback to placeholder, then final fallback. Ensure it's never empty.
                     const currentText = this.value || this.placeholder || 'Digite seu texto personalizado aqui...';
                     previewTextElement.textContent = currentText || 'Digite seu texto personalizado aqui...';
                 }
             });

             // Initial population of preview text when page loads (if textarea has value or placeholder)
             const fieldId = textArea.dataset.fieldId;
             const previewTextElement = document.querySelector(`#font-preview-${fieldId} .font-preview-text`);
             if (previewTextElement) {
                  const currentText = textArea.value || textArea.placeholder || 'Digite seu texto personalizado aqui...';
                  previewTextElement.textContent = currentText || 'Digite seu texto personalizado aqui...'; // Ensure it's never empty
             }
         }
     });
     
     // Function to automatically adjust font size to fit container
     function adjustFontSizeForPreview(element, text) {
         // Reset font size classes first
         element.style.fontSize = '';
         element.classList.remove('text-xs', 'text-sm', 'text-base', 'text-lg', 'text-xl', 'text-2xl', 'text-3xl', 'text-4xl', 'text-5xl', 'text-6xl');
         
         // More conservative initial sizing based on text length
         const textLength = text.length;
         let sizeClass = 'text-base'; // Start smaller
         
         if (textLength <= 10) {
             sizeClass = 'text-5xl';
         } else if (textLength <= 15) {
             sizeClass = 'text-4xl';
         } else if (textLength <= 25) {
             sizeClass = 'text-3xl';
         } else if (textLength <= 40) {
             sizeClass = 'text-2xl';
         } else if (textLength <= 60) {
             sizeClass = 'text-xl';
         } else if (textLength <= 80) {
             sizeClass = 'text-lg';
         } else {
             sizeClass = 'text-base';
         }
         
         element.classList.add(sizeClass);
         
         // More aggressive overflow checking with multiple iterations
         const checkAndAdjust = () => {
             // Force a reflow to get accurate measurements
             element.offsetHeight;
             
             let iterations = 0;
             const maxIterations = 8;
             
             while (element.scrollHeight > element.clientHeight && iterations < maxIterations) {
                 const currentSizes = ['text-6xl', 'text-5xl', 'text-4xl', 'text-3xl', 'text-2xl', 'text-xl', 'text-lg', 'text-base', 'text-sm', 'text-xs'];
                 const currentIndex = currentSizes.findIndex(size => element.classList.contains(size));
                 
                 if (currentIndex < currentSizes.length - 1) {
                     element.classList.remove(currentSizes[currentIndex]);
                     element.classList.add(currentSizes[currentIndex + 1]);
                     element.offsetHeight; // Force reflow
                     iterations++;
                 } else {
                     break;
                 }
             }
             
             // If still overflowing, use custom smaller font size
             if (element.scrollHeight > element.clientHeight) {
                 element.classList.remove('text-xs');
                 element.style.fontSize = '0.6rem';
             }
         };
         
         // Use requestAnimationFrame for better timing
         requestAnimationFrame(checkAndAdjust);
     }

 });
 </script>

 <!-- Three.js and Keyword Cloud Script -->
 <script type="importmap">
 {
     "imports": {
         "three": "https://unpkg.com/three@0.160.0/build/three.module.js",
         "three/addons/": "https://unpkg.com/three@0.160.0/examples/jsm/"
     }
 }
 </script>
 <script type="module">
     import * as THREE from 'three';
     // We will add OrbitControls later if needed for interactivity
     // import { OrbitControls } from 'three/addons/controls/OrbitControls.js';

     const container = document.getElementById('keyword-cloud-canvas-container');
     const fallbackContainer = document.getElementById('keyword-cloud-fallback');
     const keywords = <?= json_encode($display_keywords ?? [], JSON_UNESCAPED_UNICODE | JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP) ?>;

     if (container && keywords && keywords.length > 0) {
         // Proceed with Three.js initialization
         let scene, camera, renderer, particles;
         const texts = []; // To store text meshes

         function initThreeJS() {
             try {
                 scene = new THREE.Scene();

                 const fov = 75;
                 const aspect = container.clientWidth / container.clientHeight;
                 const near = 0.1;
                 const far = 1000;
                 camera = new THREE.PerspectiveCamera(fov, aspect, near, far);
                 camera.position.z = 100;

                 renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
                 renderer.setSize(container.clientWidth, container.clientHeight);
                 renderer.setPixelRatio(window.devicePixelRatio);
                 container.appendChild(renderer.domElement);

                 const loader = new THREE.FontLoader();
                 loader.load('https://unpkg.com/three@0.160.0/examples/fonts/helvetiker_regular.typeface.json', function (font) {
                     const material = new THREE.MeshBasicMaterial({
                         color: 0x9ca3af,
                         transparent: true,
                         opacity: 0.8,
                         side: THREE.DoubleSide
                     });

                     keywords.forEach((text, index) => {
                         const shapes = font.generateShapes(text, 5);
                         const geometry = new THREE.ShapeGeometry(shapes);
                         geometry.computeBoundingBox();
                         const xMid = -0.5 * (geometry.boundingBox.max.x - geometry.boundingBox.min.x);
                         geometry.translate(xMid, 0, 0);

                         const textMesh = new THREE.Mesh(geometry, material.clone());
                         const phi = Math.acos(-1 + (2 * index) / keywords.length);
                         const theta = Math.sqrt(keywords.length * Math.PI) * phi;
                         const radius = 60;
                         textMesh.position.setFromSphericalCoords(radius, phi, theta);
                         textMesh.lookAt(camera.position);
                         scene.add(textMesh);
                         texts.push(textMesh);
                     });

                     if (texts.length > 0) {
                         animate();
                     } else {
                         console.warn("No text meshes generated for the keyword cloud.");
                         if (fallbackContainer) fallbackContainer.style.display = 'flex';
                         if (container) container.style.display = 'none';
                     }
                 },
                 undefined,
                 function ( error ) {
                     if (fallbackContainer) fallbackContainer.style.display = 'flex';
                     if (container) container.style.display = 'none';
                 });

                 window.addEventListener('resize', onWindowResize, false);

             } catch (error) {
                 if (container) container.style.display = 'none';
                 if (fallbackContainer) fallbackContainer.style.display = 'flex';
             }
         }

         function onWindowResize() {
             if (camera && renderer && container) {
                 camera.aspect = container.clientWidth / container.clientHeight;
                 camera.updateProjectionMatrix();
                 renderer.setSize(container.clientWidth, container.clientHeight);
             }
         }

         function animate() {
             requestAnimationFrame(animate);
             texts.forEach(text => {
                 text.rotation.y += 0.002;
                 text.lookAt(camera.position);
             });
             scene.rotation.x += 0.0005;
             scene.rotation.y += 0.001;
             if (renderer && scene && camera) {
                 renderer.render(scene, camera);
             }
         }

         initThreeJS();

     } else {
         // Fallback if container or keywords are missing
         if (fallbackContainer) fallbackContainer.style.display = 'flex';
         console.warn('Keyword cloud container or keywords not found, showing fallback.');
     }

     function initThreeJS() {
         try {
             scene = new THREE.Scene();

             const fov = 75;
             const aspect = container.clientWidth / container.clientHeight;
             const near = 0.1;
             const far = 1000;
             camera = new THREE.PerspectiveCamera(fov, aspect, near, far);
             camera.position.z = 100; // Adjusted for better initial view of text

             renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
             renderer.setSize(container.clientWidth, container.clientHeight);
             renderer.setPixelRatio(window.devicePixelRatio);
             container.appendChild(renderer.domElement);

             // Create text sprites
             const loader = new THREE.FontLoader();
             // Using a default font path for now, this might need adjustment or a more robust solution
             loader.load('https://unpkg.com/three@0.160.0/examples/fonts/helvetiker_regular.typeface.json', function (font) {
                 const material = new THREE.MeshBasicMaterial({
                     color: 0x9ca3af, // text-gray-400
                     transparent: true,
                     opacity: 0.8,
                     side: THREE.DoubleSide
                 });

                 keywords.forEach((text, index) => {
                     const shapes = font.generateShapes(text, 5); // Size 5 for smaller text
                     const geometry = new THREE.ShapeGeometry(shapes);
                     geometry.computeBoundingBox();
                     const xMid = -0.5 * (geometry.boundingBox.max.x - geometry.boundingBox.min.x);
                     geometry.translate(xMid, 0, 0);

                     const textMesh = new THREE.Mesh(geometry, material.clone()); // Clone material for individual opacity/color later

                     // Distribute text in a sphere, ensure they are not too far initially
                     const phi = Math.acos(-1 + (2 * index) / keywords.length);
                     const theta = Math.sqrt(keywords.length * Math.PI) * phi;

                     const radius = 60; // Radius of the sphere
                     textMesh.position.setFromSphericalCoords(radius, phi, theta);

                     // Make text face the camera (simple billboard)
                     textMesh.lookAt(camera.position);

                     scene.add(textMesh);
                     texts.push(textMesh);
                 });
                 // Start animation only after texts are created and added
                 if (texts.length > 0) {
                     animate();
                 } else {
                     // Fallback if no texts were generated (e.g., empty keywords or font issue)
                      console.warn("No text meshes generated for the keyword cloud.");
                      if (fallbackContainer) fallbackContainer.style.display = 'flex';
                      if (container) container.style.display = 'none'; // Hide canvas if it's empty
                 }
             },
             undefined, // onProgress callback (optional)
             function ( error ) { // onError callback
                 if (fallbackContainer) fallbackContainer.style.display = 'flex';
                 if (container) container.style.display = 'none';
             });

             window.addEventListener('resize', onWindowResize, false);
             // animate(); // Moved into FontLoader callback

         } catch (error) {
             container.style.display = 'none'; // Hide canvas container on error
             if (fallbackContainer) fallbackContainer.style.display = 'flex'; // Show fallback
         }
     }

     function onWindowResize() {
         if (camera && renderer && container) {
             camera.aspect = container.clientWidth / container.clientHeight;
             camera.updateProjectionMatrix();
             renderer.setSize(container.clientWidth, container.clientHeight);
         }
     }

     function animate() {
         requestAnimationFrame(animate);

         // Rotate the whole scene or individual texts
         texts.forEach(text => {
             text.rotation.y += 0.002; // Slow rotation for each text element
             text.lookAt(camera.position); // Keep facing camera
         });

         scene.rotation.x += 0.0005;
         scene.rotation.y += 0.001;

         if (renderer && scene && camera) {
             renderer.render(scene, camera);
         }
     }

     if (keywords.length > 0) {
         initThreeJS();
     } else {
          if (fallbackContainer) fallbackContainer.style.display = 'flex';
     }
 </script>