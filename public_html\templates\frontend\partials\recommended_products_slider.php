<?php
// Include the recommended products functions
require_once __DIR__ . '/../../../includes/recommended_products_functions.php';

// Get recommended products in random order
$recommended_products = get_recommended_products_random();

// Only show the slider if we have recommended products
if (empty($recommended_products)) {
    return;
}
?>

<!-- Recommended Products Section -->
<div class="mt-16 pt-12">
    <div class="container mx-auto px-4">
        <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Produtos Recomendados</h2>
            <p class="text-gray-600 max-w-2xl mx-auto">Descubra nossa seleção especial de produtos escolhidos especialmente para si</p>
        </div>

        <!-- Recommended Products Slider -->
        <div class="recommended-slider-container relative mb-8">
        <div class="recommended-slider overflow-hidden">
            <div class="recommended-slider-track flex transition-transform duration-500" id="recommendedSliderTrack">
                <?php foreach ($recommended_products as $index => $product):
                    $product_url = add_session_param_to_url(BASE_URL . '/index.php?view=product&slug=' . $product['slug']);
                    $image_url = $product['display_image_url'];
                ?>
                    <div class="recommended-slide flex-shrink-0 w-full md:w-1/2 lg:w-1/3 xl:w-1/4 px-3">
                        <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 overflow-hidden h-full">
                            <a href="<?= $product_url ?>" class="block">
                                <div class="aspect-square overflow-hidden">
                                    <?php if ($image_url): ?>
                                        <img src="<?= htmlspecialchars($image_url) ?>" 
                                             alt="<?= htmlspecialchars($product['name_pt']) ?>" 
                                             class="w-full h-full object-cover hover:scale-105 transition-transform duration-300">
                                    <?php else: ?>
                                        <div class="w-full h-full bg-gray-100 flex items-center justify-center">
                                            <i class="ri-image-line text-4xl text-gray-400"></i>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                
                                <div class="p-4">
                                    <h3 class="font-semibold text-gray-900 mb-2 line-clamp-2 hover:text-primary transition-colors">
                                        <?= htmlspecialchars($product['name_pt']) ?>
                                    </h3>
                                    
                                    <div class="flex items-center justify-between">
                                        <div class="text-lg font-bold text-primary">
                                            €<?= number_format($product['base_price'], 2, ',', '.') ?>
                                        </div>
                                        
                                        <div class="flex items-center text-sm text-gray-500">
                                            <i class="ri-star-fill text-yellow-400 mr-1"></i>
                                            <span>Recomendado</span>
                                        </div>
                                    </div>
                                    
                                    <?php if ($product['product_type'] === 'digital'): ?>
                                        <div class="mt-2">
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                <i class="ri-download-line mr-1"></i>
                                                Digital
                                            </span>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </a>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- Slider Navigation -->
        <button class="recommended-slider-prev absolute top-1/2 left-0 transform -translate-y-1/2 bg-primary text-white w-10 h-10 rounded-full flex items-center justify-center focus:outline-none z-10 hover:bg-secondary transition-colors">
            <i class="ri-arrow-left-s-line text-xl"></i>
        </button>
        <button class="recommended-slider-next absolute top-1/2 right-0 transform -translate-y-1/2 bg-primary text-white w-10 h-10 rounded-full flex items-center justify-center focus:outline-none z-10 hover:bg-secondary transition-colors">
            <i class="ri-arrow-right-s-line text-xl"></i>
        </button>

        <!-- Slider Dots -->
        <div class="recommended-slider-dots flex justify-center mt-6">
            <?php foreach ($recommended_products as $index => $product): ?>
                <button class="recommended-slider-dot w-3 h-3 rounded-full bg-gray-300 mx-1 focus:outline-none hover:bg-gray-400 transition-colors <?= $index === 0 ? 'active bg-primary' : '' ?>" data-index="<?= $index ?>"></button>
            <?php endforeach; ?>
        </div>
    </div>
    </div>
</div>

<!-- Recommended Products Slider JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const track = document.getElementById('recommendedSliderTrack');
    const slides = document.querySelectorAll('.recommended-slide');
    const dots = document.querySelectorAll('.recommended-slider-dot');
    const prevButton = document.querySelector('.recommended-slider-prev');
    const nextButton = document.querySelector('.recommended-slider-next');

    if (!track || slides.length === 0) return;

    // Create a true infinite loop by duplicating slides at both ends
    function setupInfiniteLoop() {
        // Get original slides
        const originalSlides = Array.from(slides);
        const slideCount = originalSlides.length;

        // If we have only one slide, hide navigation
        if (slideCount <= 1) {
            if (prevButton) prevButton.style.display = 'none';
            if (nextButton) nextButton.style.display = 'none';
            if (dots.length > 0) dots[0].parentElement.style.display = 'none';
            return;
        }

        // Clone the last slide and add it to the beginning
        const lastSlideClone = originalSlides[slideCount - 1].cloneNode(true);
        lastSlideClone.classList.add('cloned-slide');
        track.insertBefore(lastSlideClone, track.firstChild);

        // Clone the first slide and add it to the end
        const firstSlideClone = originalSlides[0].cloneNode(true);
        firstSlideClone.classList.add('cloned-slide');
        track.appendChild(firstSlideClone);
    }

    setupInfiniteLoop();

    // Get all slides including clones
    const allSlides = document.querySelectorAll('.recommended-slide');
    const originalSlideCount = slides.length;

    // Start at index 1 (which is the first real slide after the clone)
    let currentIndex = 1;
    let slideWidth = allSlides[0].offsetWidth;
    let autoplayInterval;
    const autoplayDelay = 4000; // 4 seconds
    let isTransitioning = false;

    // Function to update slide positions
    function updateSlidePosition(skipTransition = false) {
        if (skipTransition) {
            track.style.transition = 'none';
        } else {
            track.style.transition = 'transform 0.5s ease';
        }

        track.style.transform = `translateX(-${currentIndex * slideWidth}px)`;

        // Update active dot - map to original slide index (0-based)
        const activeDotIndex = (currentIndex - 1 + originalSlideCount) % originalSlideCount;
        dots.forEach((dot, index) => {
            dot.classList.toggle('active', index === activeDotIndex);
            dot.classList.toggle('bg-primary', index === activeDotIndex);
            dot.classList.toggle('bg-gray-300', index !== activeDotIndex);
        });
    }

    // Function to go to next slide
    function nextSlide() {
        if (isTransitioning) return;
        
        isTransitioning = true;
        currentIndex++;
        updateSlidePosition();

        // If we're at the cloned first slide (at the end), jump to the real first slide
        setTimeout(() => {
            if (currentIndex >= allSlides.length - 1) {
                currentIndex = 1;
                updateSlidePosition(true);
            }
            isTransitioning = false;
        }, 500);
    }

    // Function to go to previous slide
    function prevSlide() {
        if (isTransitioning) return;
        
        isTransitioning = true;
        currentIndex--;
        updateSlidePosition();

        // If we're at the cloned last slide (at the beginning), jump to the real last slide
        setTimeout(() => {
            if (currentIndex <= 0) {
                currentIndex = allSlides.length - 2;
                updateSlidePosition(true);
            }
            isTransitioning = false;
        }, 500);
    }

    // Function to go to specific slide
    function goToSlide(index) {
        if (isTransitioning) return;
        
        isTransitioning = true;
        currentIndex = index + 1; // Add 1 to account for the cloned slide at the beginning
        updateSlidePosition();
        
        setTimeout(() => {
            isTransitioning = false;
        }, 500);
    }

    // Event listeners
    if (nextButton) {
        nextButton.addEventListener('click', nextSlide);
    }

    if (prevButton) {
        prevButton.addEventListener('click', prevSlide);
    }

    dots.forEach((dot, index) => {
        dot.addEventListener('click', () => goToSlide(index));
    });

    // Auto-play functionality
    function startAutoplay() {
        stopAutoplay();
        autoplayInterval = setInterval(nextSlide, autoplayDelay);
    }

    function stopAutoplay() {
        if (autoplayInterval) {
            clearInterval(autoplayInterval);
            autoplayInterval = null;
        }
    }

    // Handle window resize
    function handleResize() {
        slideWidth = allSlides[0].offsetWidth;
        updateSlidePosition(true);
    }

    window.addEventListener('resize', handleResize);

    // Initialize slider - position at first real slide
    updateSlidePosition(true);
    startAutoplay();

    // Pause autoplay when hovering over slider
    const sliderContainer = document.querySelector('.recommended-slider-container');
    if (sliderContainer) {
        sliderContainer.addEventListener('mouseenter', stopAutoplay);
        sliderContainer.addEventListener('mouseleave', startAutoplay);
    }
});
</script>

<style>
.recommended-slider {
    position: relative;
    overflow: hidden;
    padding: 0 40px;
}

.recommended-slider-track {
    display: flex;
    transition: transform 0.5s ease;
}

.recommended-slider-dot.active {
    transform: scale(1.2);
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

@media (max-width: 768px) {
    .recommended-slide {
        width: 100%;
    }
    
    .recommended-slider {
        padding: 0 20px;
    }
}

@media (max-width: 640px) {
    .recommended-slider {
        padding: 0 10px;
    }
}
</style>
