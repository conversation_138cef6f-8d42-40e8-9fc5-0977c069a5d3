<?php

require_once __DIR__ . '/../config.php';
require_once __DIR__ . '/db.php';
require_once __DIR__ . '/functions.php';

/**
 * Natural sorting function for product attribute values that may contain numbers
 * Handles cases like "1 cm", "2 cm", "10 cm", "20 cm" to sort numerically instead of alphabetically
 *
 * @param array $values Array of values to sort (key => value pairs)
 * @return array Sorted array maintaining key-value associations
 */
function sort_attribute_values_naturally(array $values): array
{
    uksort($values, function($a, $b) use ($values) {
        $value_a = $values[$a];
        $value_b = $values[$b];

        // Extract numeric values from the strings for comparison
        preg_match('/(\d+(?:\.\d+)?)/', $value_a, $matches_a);
        preg_match('/(\d+(?:\.\d+)?)/', $value_b, $matches_b);

        // If both values contain numbers, compare numerically
        if (!empty($matches_a) && !empty($matches_b)) {
            $num_a = (float)$matches_a[1];
            $num_b = (float)$matches_b[1];

            if ($num_a != $num_b) {
                return $num_a <=> $num_b;
            }

            // If numbers are equal, fall back to string comparison
            return strcasecmp($value_a, $value_b);
        }

        // If only one contains a number, prioritize the one with number
        if (!empty($matches_a) && empty($matches_b)) {
            return -1;
        }
        if (empty($matches_a) && !empty($matches_b)) {
            return 1;
        }

        // If neither contains numbers, use standard string comparison
        return strcasecmp($value_a, $value_b);
    });

    return $values;
}

function calculate_variation_price(int $product_id, int $variation_id): float|false
{
    if ($product_id <= 0 || $variation_id <= 0) {
        return false;
    }

    $product_sql = "SELECT base_price FROM products WHERE id = :product_id AND is_active = 1";
    $product = db_query($product_sql, [':product_id' => $product_id], true);

    if (!$product) {
        return false;
    }

    $base_price = (float)$product['base_price'];

    $variation_sql = "SELECT price_modifier_override FROM product_variations WHERE id = :variation_id";
    $variation = db_query($variation_sql, [':variation_id' => $variation_id], true);

    if ($variation === false) {
        return false;
    }

    if ($variation['price_modifier_override'] !== null) {
        $price_modifier = (float)$variation['price_modifier_override'];
        return $base_price + $price_modifier;
    }

    $modifiers_sql = "SELECT SUM(av.price_modifier) as total_modifier
                     FROM variation_values vv
                     JOIN attribute_values av ON vv.value_id = av.id
                     WHERE vv.variation_id = :variation_id";

    $modifiers = db_query($modifiers_sql, [':variation_id' => $variation_id], true);

    if ($modifiers === false) {
        return false;
    }

    $total_modifier = (float)($modifiers['total_modifier'] ?? 0);

    return $base_price + $total_modifier;
}

function get_variation_attribute_string(int $variation_id): string
{
    if ($variation_id <= 0) {
        return '';
    }

    $sql = "SELECT a.name_pt as attribute_name, av.value_pt as value_name
            FROM variation_values vv
            JOIN attribute_values av ON vv.value_id = av.id
            JOIN attributes a ON av.attribute_id = a.id
            WHERE vv.variation_id = :variation_id
            ORDER BY a.name_pt, av.value_pt";

    $attributes = db_query($sql, [':variation_id' => $variation_id], false, true);

    if (!$attributes) {
        return '';
    }

    $parts = [];
    foreach ($attributes as $attr) {
        $parts[] = "{$attr['attribute_name']}: {$attr['value_name']}";
    }

    return implode(', ', $parts);
}

function check_variation_stock(int $variation_id, int $quantity): bool
{
    if ($variation_id <= 0 || $quantity <= 0) {
        return false;
    }

    $sql = "SELECT stock FROM product_variations WHERE id = :variation_id AND is_active = 1";
    $variation = db_query($sql, [':variation_id' => $variation_id], true);

    if (!$variation) {
        return false;
    }

    return (int)$variation['stock'] >= $quantity;
}

function get_product_by_id(int $id): array|false
{
    if ($id <= 0) return false;
    $sql = "SELECT * FROM products WHERE id = :id";
    $product = db_query($sql, [':id' => $id], true);
    return is_array($product) ? $product : false;
}

function get_product_by_slug(string $slug): array|false
{
    if (empty($slug)) return false;
    $sql = "SELECT * FROM products WHERE slug = :slug AND is_active = 1";
    $product = db_query($sql, [':slug' => $slug], true);
    return is_array($product) ? $product : false;
}

function get_product_images(int $product_id): array
{
    if ($product_id <= 0) return [];
    $sql = "SELECT * FROM product_images WHERE product_id = :pid ORDER BY sort_order ASC, id ASC";
    $images = db_query($sql, [':pid' => $product_id], false, true);
    return is_array($images) ? $images : [];
}

function get_product_default_image(int $product_id): array|false
{
    if ($product_id <= 0) return false;

    $sql = "SELECT * FROM product_images
            WHERE product_id = :pid AND is_default = 1
            LIMIT 1";

    $image = db_query($sql, [':pid' => $product_id], true);

    if (!$image) {
        $sql = "SELECT * FROM product_images
                WHERE product_id = :pid
                ORDER BY sort_order ASC, id ASC
                LIMIT 1";

        $image = db_query($sql, [':pid' => $product_id], true);
    }

    return is_array($image) ? $image : false;
}

function get_product_variations(int $product_id): array
{
    if ($product_id <= 0) return [];
    $sql = "SELECT * FROM product_variations WHERE product_id = :pid ORDER BY id ASC";
    $variations = db_query($sql, [':pid' => $product_id], false, true);
    return is_array($variations) ? $variations : [];
}

function get_all_attributes(): array
{
    $sql = "SELECT * FROM attributes ORDER BY name_pt ASC";
    $attributes = db_query($sql, [], false, true);
    return is_array($attributes) ? $attributes : [];
}

function save_product_custom_fields(int $product_id, array $custom_fields): bool
{
    if ($product_id <= 0) {
        return false;
    }

    if (empty($custom_fields)) {
        $custom_fields = [];
    }

    try {
        $pdo = get_db_connection();

        $pdo->beginTransaction();

        $delete_sql = "DELETE FROM product_custom_fields WHERE product_id = :pid";
        $delete_stmt = $pdo->prepare($delete_sql);
        $delete_stmt->execute([':pid' => $product_id]);

        if (!empty($custom_fields)) {

            $insert_sql = "INSERT INTO product_custom_fields (product_id, custom_field_id, price_modifier_override, sort_order, created_at)
                           VALUES (:pid, :field_id, :price_override, :sort_order, datetime('now', 'localtime'))";
            $insert_stmt = $pdo->prepare($insert_sql);

            $sort_order = 0;
            $fields_added = 0;

            foreach ($custom_fields as $field_id => $field_data) {

                if (isset($field_data['use']) && $field_data['use'] == 1) {
                    $price_override = !empty($field_data['price_modifier_override']) ? (float)$field_data['price_modifier_override'] : null;

                    $insert_stmt->execute([
                        ':pid' => $product_id,
                        ':field_id' => $field_id,
                        ':price_override' => $price_override,
                        ':sort_order' => $sort_order++
                    ]);

                    $fields_added++;
                } else {
                }
            }
        }

        $pdo->commit();
        return true;
    } catch (PDOException $e) {

        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function get_product_categories(int $product_id): array
{
    if ($product_id <= 0) return [];

    $sql = "SELECT c.id, c.name, c.slug
            FROM categories c
            JOIN product_categories pc ON c.id = pc.category_id
            WHERE pc.product_id = :pid
            ORDER BY c.name ASC";

    $categories = db_query($sql, [':pid' => $product_id], false, true);
    return is_array($categories) ? $categories : [];
}

function get_similar_products(int $current_product_id, string $current_description, int $limit = 6): array
{
    if ($current_product_id <= 0 || empty(trim($current_description))) {
        return [];
    }

    $current_description = mb_strtolower(strip_tags($current_description), 'UTF-8');
    $keywords = preg_split('/[\s,\.\-\(\)]+/', $current_description);
    $common_words = ['a', 'o', 'as', 'os', 'um', 'uma', 'de', 'do', 'da', 'dos', 'das', 'em', 'no', 'na', 'nos', 'nas', 'com', 'sem', 'para', 'por', 'e', 'ou', 'que', 'se', 'este', 'esta', 'isto', 'esse', 'essa', 'isso', 'aquele', 'aquela', 'aquilo', 'seu', 'sua', 'seus', 'suas', 'the', 'a', 'an', 'is', 'of', 'in', 'on', 'with', 'for', 'and', 'or', 'to', 'it', ''];
    $significant_keywords = array_filter($keywords, function($word) use ($common_words) {
        return !empty($word) && mb_strlen($word, 'UTF-8') > 3 && !in_array($word, $common_words);
    });
    $significant_keywords = array_unique($significant_keywords);

    if (empty($significant_keywords)) {
        return [];
    }

    $like_clauses = [];
    $params = [':current_pid' => $current_product_id];
    $i = 0;
    foreach ($significant_keywords as $keyword) {
        $param_name = ':keyword' . $i++;
        $like_clauses[] = "LOWER(p.description_pt) LIKE " . $param_name;
        $params[$param_name] = '%' . $keyword . '%';
    }
    $where_clause = implode(' OR ', $like_clauses);

    $sql = "SELECT
                p.id, p.name_pt, p.slug,
                pi.filename as image_filename
            FROM products p
            LEFT JOIN (
                SELECT product_id, filename, ROW_NUMBER() OVER(PARTITION BY product_id ORDER BY sort_order ASC, id ASC) as rn
                FROM product_images
            ) pi ON p.id = pi.product_id AND pi.rn = 1
            WHERE p.is_active = 1
              AND p.id != :current_pid
              AND ({$where_clause})";

    $potential_matches = db_query($sql, $params, false, true);

    if (!$potential_matches || empty($potential_matches)) {
        return [];
    }

    shuffle($potential_matches);
    $similar_products = array_slice($potential_matches, 0, $limit);

    foreach ($similar_products as &$prod) {
        $prod['image_url'] = $prod['image_filename'] ? get_product_image_url($prod['image_filename']) : get_asset_url('images/placeholder.png');
        $prod['url'] = get_product_url($prod['slug']);
    }
    unset($prod);

    return $similar_products;
}

function generate_random_sku(string $title, bool $is_simple = true, ?string $base_sku = null): string
{

    if (!$is_simple && !empty($base_sku)) {

        $base = substr(strtoupper($base_sku), 0, 6);

        while (strlen($base) < 6) {
            $base .= strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 1));
        }

        $unique_part = strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 4));

        return $base . $unique_part;
    }

    $words = explode(' ', $title);
    $initials = '';
    foreach ($words as $word) {
        if (!empty($word)) {
            $initials .= strtoupper(mb_substr($word, 0, 1));
        }
    }

    $length = 10; 

    $sku = substr($initials, 0, $length);

    while (strlen($sku) < $length) {
        $sku .= strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 1));
    }

    return $sku;
}

function sku_exists(string $sku, ?int $exclude_product_id = null, bool $check_variations = true): bool
{
    if (empty($sku)) return false;

    $sql = "SELECT id FROM products WHERE sku = :sku";
    $params = [':sku' => $sku];

    if ($exclude_product_id) {
        $sql .= " AND id != :id";
        $params[':id'] = $exclude_product_id;
    }

    $existing_product = db_query($sql, $params, true);
    if ($existing_product) return true;

    if ($check_variations) {
        $var_sql = "SELECT id FROM product_variations WHERE sku = :sku";
        $var_params = [':sku' => $sku];

        $existing_variation = db_query($var_sql, $var_params, true);
        if ($existing_variation) return true;
    }

    return false;
}

function generate_unique_sku(string $title, bool $is_simple = true, ?string $base_sku = null, ?int $exclude_product_id = null): string
{

    $sku = generate_random_sku($title, $is_simple, $base_sku);

    $counter = 0;
    $original_sku = $sku;

    while (sku_exists($sku, $exclude_product_id) && $counter < 10) {
        $counter++;

        
        $sku = substr($original_sku, 0, 9) . $counter;
    }

    return $sku;
}

function delete_product(int $product_id): bool
{
    if ($product_id <= 0) return false;

    $pdo = get_db_connection();
    if (!$pdo) return false;

    try {

        $pdo->beginTransaction();

        $images = get_product_images($product_id);

        $videos_sql = "SELECT * FROM product_videos WHERE product_id = :pid";
        $videos = db_query($videos_sql, [':pid' => $product_id], false, true);

        $product_data = db_query("SELECT product_type FROM products WHERE id = :id", [':id' => $product_id], true);
        $is_digital_product = $product_data && $product_data['product_type'] === 'digital';

        $digital_product = null;
        if ($is_digital_product) {
            require_once __DIR__ . '/digital_product_functions.php';
            $digital_product = get_digital_product_by_product_id($product_id);
        }

        $delete_sql = "DELETE FROM products WHERE id = :id";
        $stmt = $pdo->prepare($delete_sql);
        $stmt->execute([':id' => $product_id]);

        if ($stmt->rowCount() === 0) {

            $pdo->rollBack();
            return false;
        }

        $pdo->commit();

        if (!empty($images)) {
            $image_dir = PROJECT_ROOT . '/public/assets/images/products/';
            foreach ($images as $image) {
                $image_path = $image_dir . $image['filename'];
                if (file_exists($image_path)) {
                    if (@unlink($image_path)) {
                    } else {
                    }
                }
            }
        }

        if (!empty($videos)) {
            require_once __DIR__ . '/product_video_functions.php';
            foreach ($videos as $video) {
                if ($video['video_type'] === 'uploaded' && !empty($video['filename'])) {
                    $video_path = PROJECT_ROOT . '/public/assets/videos/products/' . $video['filename'];
                    if (file_exists($video_path)) {
                        @unlink($video_path);
                    }
                }

                if (!empty($video['thumbnail_filename'])) {
                    $thumbnail_path = PROJECT_ROOT . '/public/assets/images/video_thumbnails/' . $video['thumbnail_filename'];
                    if (file_exists($thumbnail_path)) {
                        @unlink($thumbnail_path);
                    }
                }
            }
        }

        if ($is_digital_product && $digital_product && !empty($digital_product['file_path'])) {
            $file_path = $digital_product['file_path'];
            if (file_exists($file_path)) {
                if (@unlink($file_path)) {
                } else {
                }
            }
        }

        return true;
    } catch (Exception $e) {

        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        return false;
    }
}

function get_min_max_product_prices(): array
{
    $sql = "SELECT MIN(base_price) as min_price, MAX(base_price) as max_price FROM products WHERE is_active = 1";
    $result = db_query($sql, [], true);

    if ($result && $result['min_price'] !== null && $result['max_price'] !== null) {
        return [
            'min_price' => (float)$result['min_price'],
            'max_price' => (float)$result['max_price']
        ];
    }
    return ['min_price' => 0.0, 'max_price' => 1000.0];
}

function get_all_product_seo_keywords(): array
{
    $sql = "SELECT seo_keywords FROM products WHERE is_active = 1 AND seo_keywords IS NOT NULL AND seo_keywords != ''";
    $results = db_query($sql, [], false, true);

    $all_keywords = [];
    if (is_array($results)) {
        foreach ($results as $row) {
            $keywords_string = $row['seo_keywords'];
            $keywords_array = explode(',', $keywords_string);
            foreach ($keywords_array as $keyword) {
                $trimmed_keyword = trim($keyword);
                if (!empty($trimmed_keyword)) {
                    $all_keywords[] = $trimmed_keyword;
                }
            }
        }
    }

    return array_unique($all_keywords);
}

function get_all_page_seo_keywords(): array
{
    $sql = "SELECT slug, seo_keywords FROM pages WHERE is_active = 1 AND seo_keywords IS NOT NULL AND seo_keywords != ''";
    $results = db_query($sql, [], false, true);

    $keyword_data = [];
    if (is_array($results)) {
        foreach ($results as $row) {
            $page_slug = $row['slug'];
            $keywords_string = $row['seo_keywords'];
            $keywords_array = explode(',', $keywords_string);
            foreach ($keywords_array as $keyword) {
                $trimmed_keyword = trim($keyword);
                if (!empty($trimmed_keyword)) {

                    $keyword_data[] = ['keyword' => $trimmed_keyword, 'slug' => $page_slug];
                }
            }
        }
    }

    return $keyword_data;
}

function get_all_product_images_by_type(): array
{
    $sql = "SELECT pi.id, pi.product_id, pi.filename, pi.alt_text_pt as alt_text, p.name_pt as product_name, p.slug as product_slug
            FROM product_images pi
            JOIN products p ON pi.product_id = p.id
            ORDER BY p.name_pt, pi.filename";
    $images = db_query($sql, [], false, true);

    $categorized_images = ['jpeg' => [], 'webp' => [], 'png' => [], 'other' => []];

    if (is_array($images)) {
        foreach ($images as $image) {
            $ext = strtolower(pathinfo($image['filename'], PATHINFO_EXTENSION));
            if (in_array($ext, ['jpg', 'jpeg'])) {
                $categorized_images['jpeg'][] = $image;
            } elseif ($ext === 'webp') {
                $categorized_images['webp'][] = $image;
            } elseif ($ext === 'png') {
                $categorized_images['png'][] = $image;
            } else {
                if (!empty($image['filename'])) {
                   $categorized_images['other'][] = $image;
                }
            }
        }
    }
    return $categorized_images;
}

function get_product_images_filtered(string $product_name_filter = '', string $format_filter = '', string $sort_option = 'product_name', int $limit = 20, int $offset = 0): array
{
    $conditions = [];
    $params = [];
    
    
    if (!empty($product_name_filter)) {
        $conditions[] = "p.name_pt LIKE ?";
        $params[] = "%$product_name_filter%";
    }
    
    if (!empty($format_filter)) {
        if ($format_filter === 'jpeg') {
            $conditions[] = "(LOWER(SUBSTR(pi.filename, -4)) IN ('.jpg', 'jpeg') OR LOWER(SUBSTR(pi.filename, -5)) = '.jpeg')";
        } elseif ($format_filter === 'webp') {
            $conditions[] = "LOWER(SUBSTR(pi.filename, -5)) = '.webp'";
        } elseif ($format_filter === 'png') {
            $conditions[] = "LOWER(SUBSTR(pi.filename, -4)) = '.png'";
        }
    }
    
    
    $sort_options = [
        'product_name' => 'p.name_pt ASC, pi.filename ASC',
        'filename' => 'pi.filename ASC',
        'recent' => 'pi.created_at DESC',
        'format' => 'LOWER(SUBSTR(pi.filename, -4)) ASC, p.name_pt ASC'
    ];
    
    $order_by = isset($sort_options[$sort_option]) ? $sort_options[$sort_option] : $sort_options['product_name'];
    
    
    $sql = "SELECT pi.id, pi.product_id, pi.filename, pi.alt_text_pt as alt_text, p.name_pt as product_name, p.slug as product_slug
            FROM product_images pi
            JOIN products p ON pi.product_id = p.id";
    
    if (!empty($conditions)) {
        $sql .= " WHERE " . implode(" AND ", $conditions);
    }
    
    $sql .= " ORDER BY " . $order_by;
    $sql .= " LIMIT " . (int)$limit . " OFFSET " . (int)$offset;
    
    $images = db_query($sql, $params, false, true);
    
    
    $categorized_images = ['jpeg' => [], 'webp' => [], 'png' => [], 'other' => []];
    
    if (is_array($images)) {
        foreach ($images as $image) {
            $ext = strtolower(pathinfo($image['filename'], PATHINFO_EXTENSION));
            if (in_array($ext, ['jpg', 'jpeg'])) {
                $categorized_images['jpeg'][] = $image;
            } elseif ($ext === 'webp') {
                $categorized_images['webp'][] = $image;
            } elseif ($ext === 'png') {
                $categorized_images['png'][] = $image;
            } else {
                if (!empty($image['filename'])) {
                   $categorized_images['other'][] = $image;
                }
            }
        }
    }
    
    return $categorized_images;
}

function get_product_images_count(string $product_name_filter = '', string $format_filter = ''): int
{
    $conditions = [];
    $params = [];
    
    
    if (!empty($product_name_filter)) {
        $conditions[] = "p.name_pt LIKE ?";
        $params[] = "%$product_name_filter%";
    }
    
    if (!empty($format_filter)) {
        if ($format_filter === 'jpeg') {
            $conditions[] = "(LOWER(SUBSTR(pi.filename, -4)) IN ('.jpg', 'jpeg') OR LOWER(SUBSTR(pi.filename, -5)) = '.jpeg')";
        } elseif ($format_filter === 'webp') {
            $conditions[] = "LOWER(SUBSTR(pi.filename, -5)) = '.webp'";
        } elseif ($format_filter === 'png') {
            $conditions[] = "LOWER(SUBSTR(pi.filename, -4)) = '.png'";
        }
    }
    
    
    $sql = "SELECT COUNT(*) as total
            FROM product_images pi
            JOIN products p ON pi.product_id = p.id";
    
    if (!empty($conditions)) {
        $sql .= " WHERE " . implode(" AND ", $conditions);
    }
    
    $result = db_query($sql, $params, true, true);
    return $result ? (int)$result['total'] : 0;
}

function convert_image_format_gd(int $image_id, string $target_format, int $quality): array
{
    if (!function_exists('imagecreatefromjpeg')) {
        return ['success' => false, 'message' => 'A biblioteca GD não está instalada ou configurada corretamente.'];
    }

    $image_data = db_query("SELECT filename, product_id FROM product_images WHERE id = :id", [':id' => $image_id], true);
    if (!$image_data || empty($image_data['filename'])) {
        return ['success' => false, 'message' => 'Imagem não encontrada na base de dados.'];
    }

    $original_filename = $image_data['filename'];
    
    $product_image_base_dir = defined('PRODUCT_IMAGES_UPLOAD_DIR') ? PRODUCT_IMAGES_UPLOAD_DIR : (PROJECT_ROOT . '/public/assets/images/products/');
    $source_path = $product_image_base_dir . $original_filename; 
    
    if (!file_exists($source_path)) {
        $source_path_alt = (defined('PRODUCT_IMAGES_PATH') ? PRODUCT_IMAGES_PATH : $product_image_base_dir) . $original_filename;
        if (file_exists($source_path_alt)){
            $source_path = $source_path_alt;
        } else {
             return ['success' => false, 'message' => 'Ficheiro de imagem original não encontrado no servidor. Verificado: ' . $source_path . ' e ' . $source_path_alt];
        }
    }

    $original_ext = strtolower(pathinfo($original_filename, PATHINFO_EXTENSION));
    $file_basename = pathinfo($original_filename, PATHINFO_FILENAME);
    $file_dirname = pathinfo($original_filename, PATHINFO_DIRNAME);
    
    $new_filename_only = $file_basename . '.' . $target_format;
    $new_full_filename = ($file_dirname === '.' ? '' : $file_dirname . DIRECTORY_SEPARATOR) . $new_filename_only;
    
    $destination_path = $product_image_base_dir . $new_full_filename;
    
    $destination_dir = dirname($destination_path);
    if (!is_dir($destination_dir)) {
        if (!mkdir($destination_dir, 0775, true)) {
            return ['success' => false, 'message' => 'Falha ao criar o diretório de destino: ' . $destination_dir];
        }
    }

    $image = null;
    if (in_array($original_ext, ['jpg', 'jpeg'])) {
        $image = @imagecreatefromjpeg($source_path);
    } elseif ($original_ext === 'webp') {
        $image = @imagecreatefromwebp($source_path);
    } elseif ($original_ext === 'png') {
        $image = @imagecreatefrompng($source_path);
        if ($image && $target_format !== 'png') { 
            imagepalettetotruecolor($image);
            imagealphablending($image, true);
            imagesavealpha($image, true);
        }
    } else {
        return ['success' => false, 'message' => 'Formato de imagem original não suportado para conversão: ' . $original_ext];
    }

    if (!$image) {
        return ['success' => false, 'message' => 'Falha ao carregar a imagem original com GD. O ficheiro pode estar corrompido ou ser de um tipo não suportado (' . $original_ext . '). Path: ' . $source_path];
    }

    $conversion_success = false;
    if ($target_format === 'webp') {
        $conversion_success = @imagewebp($image, $destination_path, $quality);
    } elseif (in_array($target_format, ['jpg', 'jpeg'])) {
        $conversion_success = @imagejpeg($image, $destination_path, $quality);
    } elseif ($target_format === 'png') {
        $png_quality = round((100 - $quality) / 100 * 9); 
        $conversion_success = @imagepng($image, $destination_path, $png_quality);
    } else {
        imagedestroy($image);
        return ['success' => false, 'message' => 'Formato de destino não suportado: ' . $target_format];
    }

    imagedestroy($image);

    if ($conversion_success && file_exists($destination_path)) {
        return ['success' => true, 'new_filename' => $new_full_filename, 'original_filename' => $original_filename, 'message' => 'Imagem convertida com sucesso.'];
    } else {
        if (file_exists($destination_path)) {
            @unlink($destination_path);
        }
        return ['success' => false, 'message' => 'Falha ao guardar a imagem convertida. Verifique as permissões do diretório: ' . $destination_path];
    }
}

function update_product_image_filename_and_delete_old(int $image_id, string $new_filename, string $original_filename): array
{
    $pdo = get_db_connection();
    try {
        $stmt = $pdo->prepare("UPDATE product_images SET filename = :new_filename WHERE id = :id");
        $stmt->execute([':new_filename' => $new_filename, ':id' => $image_id]);

        if ($stmt->rowCount() > 0) {
            if ($new_filename !== $original_filename) {
                $product_image_base_dir = defined('PRODUCT_IMAGES_UPLOAD_DIR') ? PRODUCT_IMAGES_UPLOAD_DIR : (PROJECT_ROOT . '/public/assets/images/products/');
                $original_path = $product_image_base_dir . $original_filename;
                if (file_exists($original_path)) {
                    if (@unlink($original_path)) {
                        return ['success' => true, 'message' => 'Base de dados atualizada e ficheiro original removido.'];
                    } else {
                        return ['success' => true, 'message' => 'Base de dados atualizada, mas falha ao remover o ficheiro original (' . $original_path . '). Verifique as permissões.'];
                    }
                }
            }
            return ['success' => true, 'message' => 'Base de dados atualizada com sucesso.'];
        } else {
            return ['success' => false, 'message' => 'Nenhuma alteração feita na base de dados. ID da imagem não encontrado ou nome do ficheiro já atualizado.'];
        }
    } catch (PDOException $e) {
        return ['success' => false, 'message' => 'Erro na base de dados ao atualizar o nome do ficheiro: ' . $e->getMessage()];
    }
}
