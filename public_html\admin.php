<?php 

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

if (ob_get_level() > 0) {
    ob_end_clean();
}
ob_start(function($buffer) {
    $is_ajax_request_for_log = isset($_GET['ajax']) && $_GET['ajax'] === '1';
    $trimmed_buffer = ltrim($buffer);

    if ($is_ajax_request_for_log && strpos($trimmed_buffer, '<![CDATA[') === 0) {
        $original_buffer_snippet = substr($trimmed_buffer, 0, 250);

        
        
        $cleaned_buffer = preg_replace('/^\\s*<!\\[CDATA\\[\\s*/s', '', $buffer, 1);
        $was_modified_start = ($cleaned_buffer !== $buffer);

        
        $final_buffer = preg_replace('/\\s*]]>\\s*$/s', '', $cleaned_buffer, 1);
        $was_modified_end = ($final_buffer !== $cleaned_buffer);

        if ($was_modified_start || $was_modified_end) {
            return $final_buffer;
        } else {
        }
    }
    return $buffer;
});

function return_bytes($size_str) {
    $size_str = trim($size_str);
    $last = strtolower($size_str[strlen($size_str) - 1]);
    $val = (int)$size_str;

    switch ($last) {
        case 'g': $val *= 1024;
        case 'm': $val *= 1024;
        case 'k': $val *= 1024;
    }

    return $val;
}

define('ADMIN_CONTEXT', true);
require_once __DIR__ . '/config.php';
require_once __DIR__ . '/includes/db.php';
require_once __DIR__ . '/includes/session.php';
require_once __DIR__ . '/includes/functions.php';
require_once __DIR__ . '/includes/security.php';
require_once __DIR__ . '/includes/payment_methods.php';
require_once __DIR__ . '/includes/product_functions.php';
require_once __DIR__ . '/includes/attribute_functions.php';
require_once __DIR__ . '/includes/order_functions.php';
require_once __DIR__ . '/includes/vat_functions.php';
require_once __DIR__ . '/includes/custom_field_functions.php';
require_once __DIR__ . '/includes/blog_functions.php';
require_once __DIR__ . '/includes/digital_product_functions.php';
require_once __DIR__ . '/includes/session.php';

$current_session_id = start_cookieless_session();
$settings = load_settings();

$is_admin_logged_in = false;
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    $is_admin_logged_in = true;
}

if ($is_admin_logged_in && isset($_SESSION['redirect_to'])) {
    $redirect_url = $_SESSION['redirect_to'];
    unset($_SESSION['redirect_to']);
    header('Location: ' . $redirect_url);
    exit;
}

$login_error = null;
if (isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['login'])) {
    $password = $_POST['password'] ?? '';
    $submitted_csrf = $_POST['csrf_token'] ?? '';

    if (!validate_csrf_token($submitted_csrf)) {
        $login_error = "Erro de segurança. Por favor, tente novamente.";
    } else {

        $stored_hash = get_setting('admin_password_hash');

        if (empty($stored_hash)) {

            if (!empty($password)) {
                $new_hash = hash_password($password);
                if ($new_hash && update_setting('admin_password_hash', $new_hash)) {
                    $_SESSION['admin_logged_in'] = true;
                    $is_admin_logged_in = true;
                    session_regenerate_id(true);
                    $current_session_id = session_id();
                    header('Location: admin.php?' . get_session_id_param());
                    exit;
                } else {
                    $login_error = "Erro ao definir a password inicial.";
                }
            } else {
                 $login_error = "Password de administrador não definida. Introduza uma password para a definir.";
            }

        } elseif (verify_password($password, $stored_hash)) {

            $_SESSION['admin_logged_in'] = true;
            $is_admin_logged_in = true;
            session_regenerate_id(true);
            $current_session_id = session_id();
            header('Location: admin.php?' . get_session_id_param());
            exit;
        } else {

            $login_error = "Password inválida.";
        }
    }
    unset($_SESSION['csrf_token']);
    generate_csrf_token();
}

if (isset($_GET['action']) && $_GET['action'] === 'logout') {

    session_unset();

    destroy_session($current_session_id);

    session_destroy();

    $is_ajax = (isset($_SERVER['HTTP_X_REQUESTED_WITH']) &&
                strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest');

    if ($is_ajax) {

        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'redirect' => 'admin.php'
        ]);
    } else {

        header('Location: admin.php');
    }
    exit;
}

$section = $_GET['section'] ?? ($_POST['section'] ?? 'dashboard');
$action = $_GET['action'] ?? ($_POST['action'] ?? 'list');

if ($is_admin_logged_in && isset($_SERVER['REQUEST_METHOD']) && $_SERVER['REQUEST_METHOD'] === 'POST' && $section === 'products' && $action === 'convert_image') {
    if (!isset($_POST['form_token']) || !validate_csrf_token($_POST['form_token'])) {
        add_flash_message('Erro de segurança (token inválido). Por favor, tente novamente.', 'danger');
    } else {
        $image_ids = $_POST['image_ids'] ?? [];
        $target_format = $_POST['target_format'] ?? ''; 
        $quality = isset($_POST['quality']) ? (int)$_POST['quality'] : 80;

        if (empty($image_ids)) {
            add_flash_message('Nenhuma imagem selecionada para conversão.', 'warning');
        } elseif (!in_array($target_format, ['webp', 'jpg', 'jpeg'])) {
            add_flash_message('Formato de destino inválido.', 'danger');
        } elseif ($quality < 0 || $quality > 100) {
            add_flash_message('Qualidade inválida. Deve ser entre 0 e 100.', 'danger');
        } else {
            $success_count = 0;
            $error_count = 0;
            $conversion_messages = [];

            foreach ($image_ids as $image_id) {
                $image_id = (int)$image_id;
                $result = convert_image_format_gd($image_id, $target_format, $quality);

                if ($result['success']) {
                    $update_db_result = update_product_image_filename_and_delete_old($image_id, $result['new_filename'], $result['original_filename']);
                    if ($update_db_result['success']) {
                        $success_count++;
                        $conversion_messages[] = "Imagem ID {$image_id} ({$result['original_filename']}) convertida para {$result['new_filename']}. " . $update_db_result['message'];
                    } else {
                        $error_count++;
                        
                        $product_image_base_dir = defined('PRODUCT_IMAGES_UPLOAD_DIR') ? PRODUCT_IMAGES_UPLOAD_DIR : (PROJECT_ROOT . '/public/assets/images/products/');
                        $new_file_path = $product_image_base_dir . $result['new_filename'];
                        if (file_exists($new_file_path)) {
                            @unlink($new_file_path);
                        }
                        $conversion_messages[] = "Imagem ID {$image_id}: Conversão física OK, mas falha ao atualizar BD: " . $update_db_result['message'];
                    }
                } else {
                    $error_count++;
                    $conversion_messages[] = "Imagem ID {$image_id}: Falha na conversão - " . $result['message'];
                }
            }

            if ($success_count > 0) {
                add_flash_message("{$success_count} imagem(ns) convertida(s) com sucesso.", 'success');
            }
            if ($error_count > 0) {
                add_flash_message("{$error_count} imagem(ns) falharam na conversão.", 'danger');
            }
            if (!empty($conversion_messages)) {
                foreach($conversion_messages as $msg) { 
                }
                
                 add_flash_message("Detalhes da conversão registados.", 'info');
            }
        }
    }
    
    header('Location: admin.php?section=products&action=images&' . get_session_id_param());
    exit;
}

$item_id = isset($_GET['id']) ? (int)$_GET['id'] : (isset($_GET['item_id']) ? (int)$_GET['item_id'] : (isset($_POST['id']) ? (int)$_POST['id'] : (isset($_POST['item_id']) ? (int)$_POST['item_id'] : null)));

$is_ajax_request = (
    (isset($_GET['ajax']) && $_GET['ajax'] === '1') ||
    (isset($_POST['is_ajax']) && $_POST['is_ajax'] === '1') ||
    (isset($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest')
);

if ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'GET' &&
    $section === 'products' && $action === 'get_attribute_values' &&
    isset($_GET['attribute_id'])) {

    
    while (ob_get_level() > 0) {
        ob_end_clean();
    }

    require_once __DIR__ . '/includes/attribute_functions.php';

    $attribute_id = (int)$_GET['attribute_id'];
    if (!$attribute_id) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => 'ID do atributo em falta.']);
        exit;
    }

    try {
        $values = get_attribute_values($attribute_id);

        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'values' => $values
        ]);
    } catch (Exception $e) {
        header('Content-Type: application/json');
        echo json_encode([
            'success' => false,
            'message' => 'Erro ao buscar valores do atributo: ' . $e->getMessage()
        ]);
    }
    exit;
}

if ($is_admin_logged_in && $is_ajax_request && $_SERVER['REQUEST_METHOD'] === 'POST' && 
    !($section === 'orders' && $action === 'regenerate_token')) {
    
    while (ob_get_level() > 0) {
        ob_end_clean();
    }

    require_once __DIR__ . '/includes/admin_ajax_handler.php';
    exit;
}

$admin_page_title = "Admin - " . ($settings['store_name'] ?? 'eShop');

function prepare_admin_view_data($section, $action, $item_id, $settings, $current_session_id, $is_admin_logged_in, $admin_page_title) {
    $admin_view_data = [
        'settings' => $settings,
        'current_session_id' => $current_session_id,
        'is_admin_logged_in' => $is_admin_logged_in,
        'section' => $section,
        'action' => $action,
        'item_id' => $item_id
    ];

    switch ($section) {
        case 'pages':
            $admin_view_data['page_title'] = "Gerir Páginas - " . $admin_page_title;
            if ($action === 'edit' && $item_id) {
                $admin_view_data['page_data'] = get_page_by_id($item_id);
                if (!$admin_view_data['page_data']) {
                    add_flash_message("Página não encontrada.", 'danger');

                    $admin_view_data['redirect_to'] = 'admin.php?section=pages&' . get_session_id_param();
                }
                $admin_view_data['categories'] = get_all_page_categories();
            } elseif ($action === 'new') {
                $admin_view_data['categories'] = get_all_page_categories();
            } elseif ($action === 'list' || $action === '') {

                $title_filter = isset($_GET['title']) ? trim($_GET['title']) : '';
                $category_filter = isset($_GET['category_id']) ? $_GET['category_id'] : '';
                $status_filter = isset($_GET['status']) ? $_GET['status'] : '';
                $location_filter = isset($_GET['location']) ? $_GET['location'] : '';

                $current_page = isset($_GET['p']) ? max(1, (int)$_GET['p']) : 1;
                $per_page = 20;

                $filters = [];
                if (!empty($title_filter)) {
                    $filters['title'] = $title_filter;
                }
                if (!empty($category_filter)) {
                    $filters['category_id'] = $category_filter;
                }
                if ($status_filter !== '') {
                    $filters['is_active'] = $status_filter;
                }
                if (!empty($location_filter)) {
                    $filters['location'] = $location_filter;
                }

                $filters['page'] = $current_page;
                $filters['per_page'] = $per_page;

                $admin_view_data['pages'] = get_all_pages($filters);
            }
            break;

        case 'page_categories':
            $admin_view_data['page_title'] = "Categorias de Páginas - " . $admin_page_title;
            if ($action === 'edit' && $item_id) {
                $admin_view_data['category_data'] = get_page_category_by_id($item_id);
                if (!$admin_view_data['category_data']) {
                    add_flash_message("Categoria não encontrada.", 'danger');

                    $admin_view_data['redirect_to'] = 'admin.php?section=page_categories&' . get_session_id_param();
                }
            } elseif ($action === 'list' || $action === '') {
                $admin_view_data['categories'] = get_all_page_categories();
            }
            break;

        case 'page_placeholders':
            $admin_view_data['page_title'] = "Placeholders de Páginas - " . $admin_page_title;
            if ($action === 'edit' && $item_id) {
                $admin_view_data['placeholder_data'] = get_page_placeholder_by_id($item_id);
                if (!$admin_view_data['placeholder_data']) {
                    add_flash_message("Placeholder não encontrado.", 'danger');

                    $admin_view_data['redirect_to'] = 'admin.php?section=page_placeholders&' . get_session_id_param();
                }
            } elseif ($action === 'list' || $action === '') {
                $admin_view_data['placeholders'] = get_all_page_placeholders();
            }
            break;

        case 'placeholder_links':
            $admin_view_data['page_title'] = "Links de Placeholders - " . $admin_page_title;
            if ($action === 'edit' && $item_id) {
                $admin_view_data['link_data'] = get_placeholder_link_by_id($item_id);
                if (!$admin_view_data['link_data']) {
                    add_flash_message("Link não encontrado.", 'danger');

                    $admin_view_data['redirect_to'] = 'admin.php?section=placeholder_links&' . get_session_id_param();
                }
                $admin_view_data['placeholders'] = get_all_page_placeholders();
            } elseif ($action === 'new') {
                $admin_view_data['placeholders'] = get_all_page_placeholders();

                if (isset($_GET['placeholder_id'])) {
                    $admin_view_data['selected_placeholder_id'] = (int)$_GET['placeholder_id'];
                }
            } elseif ($action === 'list' || $action === '') {

                $admin_view_data['placeholders'] = get_all_page_placeholders();

                $placeholders_with_links = [];
                foreach ($admin_view_data['placeholders'] as $placeholder) {
                    $links = get_links_by_placeholder_id($placeholder['id']);
                    $placeholders_with_links[] = [
                        'id' => $placeholder['id'],
                        'name' => $placeholder['name'],
                        'slug' => $placeholder['slug'],
                        'links' => $links
                    ];
                }
                $admin_view_data['placeholders_with_links'] = $placeholders_with_links;
            }
            break;

        case 'products':
            $admin_view_data['page_title'] = "Gerir Produtos - " . $admin_page_title;
            if ($action === 'edit' && $item_id) {
                $admin_view_data['product'] = get_product_by_id($item_id);
                if (!$admin_view_data['product']) {
                    add_flash_message("Produto não encontrado.", 'danger');
                    $admin_view_data['redirect_to'] = 'admin.php?section=products&' . get_session_id_param();
                } else {
                    $admin_view_data['product_images'] = get_product_images($item_id);
                    $admin_view_data['variations'] = get_product_variations($item_id);
                    $admin_view_data['all_attributes'] = get_all_attributes();

                    require_once __DIR__ . '/includes/product_info_fields.php';
                    $admin_view_data['product_info_fields'] = get_product_info_fields($item_id);
                    $admin_view_data['all_categories'] = getAllCategories();
                    
                    $admin_view_data['product_custom_fields'] = get_product_custom_fields($item_id);
                    
                    $admin_view_data['all_custom_fields'] = get_custom_fields(true);
                }
            } elseif ($action === 'new') {
                $admin_view_data['all_attributes'] = get_all_attributes();
                $admin_view_data['all_categories'] = getAllCategories();
                
                $admin_view_data['all_custom_fields'] = get_custom_fields(true);
                
                $admin_view_data['product_custom_fields'] = [];
            } elseif ($action === 'images') {
                $admin_view_data['page_title'] = "Gerir Imagens de Produtos - " . $admin_page_title;
                
                
                $product_name_filter = isset($_GET['product_name']) ? trim($_GET['product_name']) : '';
                $sort_option = isset($_GET['sort']) ? $_GET['sort'] : 'product_name';
                $active_tab = isset($_GET['tab']) ? $_GET['tab'] : 'jpeg-to-webp';
                
                
                $images_per_page = 20;
                
                
                $jpeg_page = isset($_GET['jpeg_p']) ? max(1, (int)$_GET['jpeg_p']) : 1;
                $jpeg_offset = ($jpeg_page - 1) * $images_per_page;
                $jpeg_format_filter = 'jpeg'; 
                
                $jpeg_images_result = get_product_images_filtered($product_name_filter, $jpeg_format_filter, $sort_option, $images_per_page, $jpeg_offset);
                $total_jpeg_images = get_product_images_count($product_name_filter, $jpeg_format_filter);
                $total_jpeg_pages = ceil($total_jpeg_images / $images_per_page);
                
                
                $webp_page = isset($_GET['webp_p']) ? max(1, (int)$_GET['webp_p']) : 1;
                $webp_offset = ($webp_page - 1) * $images_per_page;
                $webp_format_filter = 'webp'; 
                
                $webp_images_result = get_product_images_filtered($product_name_filter, $webp_format_filter, $sort_option, $images_per_page, $webp_offset);
                $total_webp_images = get_product_images_count($product_name_filter, $webp_format_filter);
                $total_webp_pages = ceil($total_webp_images / $images_per_page);
                
                
                $jpeg_images = array_merge(
                    $jpeg_images_result['jpeg'] ?? [],
                    $jpeg_images_result['png'] ?? []
                );
                $webp_images = $webp_images_result['webp'] ?? [];
                
                
                $categorized_images = [
                    'jpeg' => $jpeg_images_result['jpeg'] ?? [],
                    'png' => $jpeg_images_result['png'] ?? [],
                    'webp' => $webp_images_result['webp'] ?? []
                ];
                
                $admin_view_data['product_images_categorized'] = $categorized_images;
                $admin_view_data['jpeg_images'] = $jpeg_images;
                $admin_view_data['webp_images'] = $webp_images;
                $admin_view_data['product_name_filter'] = $product_name_filter;
                $admin_view_data['sort_option'] = $sort_option;
                $admin_view_data['active_tab'] = $active_tab;
                
                
                $admin_view_data['jpeg_current_page'] = $jpeg_page;
                $admin_view_data['jpeg_total_pages'] = $total_jpeg_pages;
                $admin_view_data['jpeg_total_images'] = $total_jpeg_images;
                
                
                $admin_view_data['webp_current_page'] = $webp_page;
                $admin_view_data['webp_total_pages'] = $total_webp_pages;
                $admin_view_data['webp_total_images'] = $total_webp_images;
                
                $admin_view_data['template_file'] = 'products_images.php';
            }
            break;

        case 'orders':
            $admin_view_data['page_title'] = "Gerir Encomendas - " . $admin_page_title;
            if ($action === 'detail' && $item_id) {
                $admin_view_data['order'] = get_order_by_id($item_id);
                if (!$admin_view_data['order']) {
                    add_flash_message("Encomenda não encontrada.", 'danger');
                    $admin_view_data['redirect_to'] = 'admin.php?section=orders&' . get_session_id_param();
                } else {
                    $admin_view_data['order_items'] = get_order_items($item_id);
                }
            } else {
                $admin_view_data['orders'] = get_all_orders();
            }
            break;

        case 'categories':
            $admin_view_data['page_title'] = "Categorias de Produtos - " . $admin_page_title;
            if ($action === 'edit' && $item_id) {
                $admin_view_data['category_data'] = getCategoryById($item_id);
                if (!$admin_view_data['category_data']) {
                    add_flash_message("Categoria de produto não encontrada.", 'danger');
                    $admin_view_data['redirect_to'] = 'admin.php?section=categories&' . get_session_id_param();
                }
            } elseif ($action === 'list' || $action === '') {
                $admin_view_data['categories'] = getAllCategories();
            }
            break;

        case 'messages':
            $admin_view_data['page_title'] = "Mensagens - " . $admin_page_title;
            $admin_view_data['message_history'] = get_messages_with_replies('created_at', 'DESC');
            break;

        case 'vat_rates':
            $admin_view_data['page_title'] = "Taxas de IVA - " . $admin_page_title;
            $admin_view_data['vat_rates'] = vat_get_all_rates();
            break;

        case 'payment_methods':
            $admin_view_data['page_title'] = "Métodos de Pagamento - " . $admin_page_title;

            ensure_payment_methods_table_exists();

            $admin_view_data['payment_methods'] = get_payment_methods();

            if ($action === 'edit' && $item_id) {
                $admin_view_data['payment_method'] = get_payment_method($item_id);
                if (!$admin_view_data['payment_method']) {
                    add_flash_message("Método de pagamento não encontrado.", 'danger');
                    $admin_view_data['redirect_to'] = 'admin.php?section=payment_methods&' . get_session_id_param();
                }
            }
            break;

        case 'attributes':
            $admin_view_data['page_title'] = "Gerir Atributos - " . $admin_page_title;
            if ($action === 'edit' && $item_id) {
                $admin_view_data['attribute_data'] = get_attribute_by_id($item_id);
                if (!$admin_view_data['attribute_data']) {
                    add_flash_message("Atributo não encontrado.", 'danger');
                    $admin_view_data['redirect_to'] = 'admin.php?section=attributes&' . get_session_id_param();
                }
            } elseif ($action === 'values' && isset($_GET['attribute_id'])) {
                $attribute_id = (int)$_GET['attribute_id'];
                $admin_view_data['attribute'] = get_attribute_by_id($attribute_id);
                if (!$admin_view_data['attribute']) {
                    add_flash_message("Atributo não encontrado.", 'danger');
                    $admin_view_data['redirect_to'] = 'admin.php?section=attributes&' . get_session_id_param();
                } else {
                    $admin_view_data['attribute_values'] = get_attribute_values($attribute_id);
                }
            }
            break;

        case 'custom_fields':
            $admin_view_data['page_title'] = "Campos Personalizados - " . $admin_page_title;
            if ($action === 'edit' && $item_id) {
                $admin_view_data['custom_field'] = get_custom_field($item_id);
                if (!$admin_view_data['custom_field']) {
                    add_flash_message("Campo personalizado não encontrado.", 'danger');
                    $admin_view_data['redirect_to'] = 'admin.php?section=custom_fields&' . get_session_id_param();
                }
                $admin_view_data['field_types'] = get_custom_field_types();
                $admin_view_data['fonts'] = get_custom_field_fonts();
            } elseif ($action === 'new') {
                $admin_view_data['field_types'] = get_custom_field_types();
                $admin_view_data['fonts'] = get_custom_field_fonts();
            } else {
                $admin_view_data['custom_fields'] = get_custom_fields(false);
            }
            break;

        case 'custom_field_fonts':
            $admin_view_data['page_title'] = "Fontes para Campos Personalizados - " . $admin_page_title;
            if ($action === 'edit' && $item_id) {
                $admin_view_data['font'] = get_custom_field_font($item_id);
                if (!$admin_view_data['font']) {
                    add_flash_message("Fonte não encontrada.", 'danger');
                    $admin_view_data['redirect_to'] = 'admin.php?section=custom_field_fonts&' . get_session_id_param();
                }
            } else {
                $admin_view_data['fonts'] = get_custom_field_fonts(false);
            }
            break;

        case 'blog_categories':
            $admin_view_data['page_title'] = "Categorias do Blog - " . $admin_page_title;
            if ($action === 'edit' && $item_id) {
                $admin_view_data['category_data'] = get_blog_category($item_id);
                if (!$admin_view_data['category_data']) {
                    add_flash_message("Categoria de blog não encontrada.", 'danger');
                    $admin_view_data['redirect_to'] = 'admin.php?section=blog_categories&' . get_session_id_param();
                }
            } elseif ($action === 'list' || $action === '') {
                $admin_view_data['categories'] = get_blog_categories();
            }
            break;

        case 'blog_posts':
            $admin_view_data['page_title'] = "Posts do Blog - " . $admin_page_title;
            if ($action === 'edit' && $item_id) {
                $admin_view_data['post_data'] = get_blog_post($item_id);
                if (!$admin_view_data['post_data']) {
                    add_flash_message("Post de blog não encontrado.", 'danger');
                    $admin_view_data['redirect_to'] = 'admin.php?section=blog_posts&' . get_session_id_param();
                }
                $admin_view_data['categories'] = get_blog_categories(true);
            } elseif ($action === 'new') {
                $admin_view_data['categories'] = get_blog_categories(true);
            } else {

                $title_filter = isset($_GET['title']) ? trim($_GET['title']) : '';
                $category_filter = isset($_GET['category_id']) ? $_GET['category_id'] : '';
                $type_filter = isset($_GET['post_type']) ? $_GET['post_type'] : '';
                $status_filter = isset($_GET['is_published']) ? $_GET['is_published'] : '';

                $current_page = isset($_GET['p']) ? max(1, (int)$_GET['p']) : 1;
                $per_page = 20;

                $options = [
                    'page' => $current_page,
                    'per_page' => $per_page,
                    'order_by' => 'published_at',
                    'order_dir' => 'DESC'
                ];

                if (!empty($title_filter)) {
                    $options['search_term'] = $title_filter;
                }
                if (!empty($category_filter)) {
                    $options['category_id'] = $category_filter;
                }
                if (!empty($type_filter)) {
                    $options['post_type'] = $type_filter;
                }
                if ($status_filter !== '') {
                    $options['is_published'] = (int)$status_filter;
                }

                $posts_result = get_blog_posts($options);
                $admin_view_data['posts'] = $posts_result['posts'];
                $admin_view_data['total_posts'] = $posts_result['total_count'];
                $admin_view_data['current_page'] = $options['page'];
                $admin_view_data['per_page'] = $options['per_page'];
            }
            break;

        case 'sitemaps':

            require_once __DIR__ . '/includes/sitemap_functions.php';

            ensure_sitemap_configs_table_exists();

            $admin_view_data['page_title'] = "Sitemaps e XMLs - " . $admin_page_title;

            if ($action === 'edit' && $item_id) {
                $admin_view_data['sitemap'] = get_sitemap_config($item_id);
                if (!$admin_view_data['sitemap']) {
                    add_flash_message("Configuração de sitemap não encontrada.", 'danger');
                    $admin_view_data['redirect_to'] = 'admin.php?section=sitemaps&' . get_session_id_param();
                }

            } else {

                $admin_view_data['sitemaps'] = get_sitemap_configs();
            }
            break;

        case 'digital_products':
            require_once __DIR__ . '/includes/digital_product_functions.php';
            require_once __DIR__ . '/includes/digital_files_functions.php';

            $admin_view_data['page_title'] = "Produtos Digitais - " . $admin_page_title;
            if ($action === 'edit' && $item_id) {

                $admin_view_data['product_data'] = get_product_by_id($item_id);
                if (!$admin_view_data['product_data']) {
                    add_flash_message("Produto não encontrado.", 'danger');
                    $admin_view_data['redirect_to'] = 'admin.php?section=products&' . get_session_id_param();
                }

                $admin_view_data['digital_product'] = get_digital_product_by_product_id($item_id);

                $admin_view_data['all_file_types'] = get_all_file_types();

                if ($admin_view_data['digital_product']) {
                    $admin_view_data['selected_file_types'] = get_digital_product_file_type_ids($admin_view_data['digital_product']['id']);
                }
            } elseif ($action === 'new' && $item_id) {

                $admin_view_data['product_data'] = get_product_by_id($item_id);
                if (!$admin_view_data['product_data']) {
                    add_flash_message("Produto não encontrado.", 'danger');
                    $admin_view_data['redirect_to'] = 'admin.php?section=products&' . get_session_id_param();
                }

                $admin_view_data['all_file_types'] = get_all_file_types();
            } elseif ($action === 'list' || empty($action)) {

                require_once __DIR__ . '/includes/digital_files_functions.php';
                $admin_view_data['digital_products'] = get_all_digital_products();
            }
            break;

        case 'digital_products':
            require_once __DIR__ . '/includes/digital_product_functions.php';
            require_once __DIR__ . '/includes/digital_files_functions.php';
            
            $admin_view_data['page_title'] = "Produtos Digitais - " . $admin_page_title;
            
            if ($action === 'edit' && $item_id) {
                $admin_view_data['product_data'] = get_product_by_id($item_id);
                if (!$admin_view_data['product_data']) {
                    add_flash_message("Produto não encontrado.", 'danger');
                    $_SESSION['redirect_to'] = 'admin.php?section=products&' . get_session_id_param();
                    return;
                }
                
                $admin_view_data['digital_product'] = get_digital_product_by_product_id($item_id);
                $admin_view_data['all_file_types'] = get_all_file_types();
                
                if ($admin_view_data['digital_product']) {
                    $admin_view_data['selected_file_types'] = get_digital_product_file_type_ids($admin_view_data['digital_product']['id']);
                }
                
                include_template('backend/digital_product_form.php', $admin_view_data);
            } elseif ($action === 'new' && $item_id) {
                $admin_view_data['product_data'] = get_product_by_id($item_id);
                if (!$admin_view_data['product_data']) {
                    add_flash_message("Produto não encontrado.", 'danger');
                    $_SESSION['redirect_to'] = 'admin.php?section=products&' . get_session_id_param();
                    return;
                }
                
                $admin_view_data['digital_product'] = null;
                $admin_view_data['all_file_types'] = get_all_file_types();
                
                include_template('backend/digital_product_form.php', $admin_view_data);
            } else {
                include_template('backend/digital_products_list.php', $admin_view_data);
            }
            break;

        case 'digital_files':
            require_once __DIR__ . '/includes/digital_files_functions.php';
            require_once __DIR__ . '/includes/digital_product_functions.php';

            $admin_view_data['page_title'] = "Gestão de Arquivos Digitais - " . $admin_page_title;

            if ($action === 'change_file' && $item_id) {

                $admin_view_data['item_id'] = $item_id;

                $digital_product = db_query("SELECT * FROM digital_products WHERE id = :id", [':id' => $item_id], true);
                if ($digital_product) {
                    $admin_view_data['digital_product'] = $digital_product;
                }
            }
            break;

        case 'download_statistics':
            $admin_view_data['page_title'] = "Estatísticas de Downloads - " . $admin_page_title;
            break;

        case 'maintenance':
            $admin_view_data['page_title'] = "Manutenção - " . $admin_page_title;
            break;

        case 'sessions':
            $admin_view_data['page_title'] = "Gerir Sessões - " . $admin_page_title;
            
            
            $country_filter = $_GET['country'] ?? '';
            $ip_filter = $_GET['ip'] ?? '';
            $sort = $_GET['sort'] ?? 'recent';
            $page = max(1, (int)($_GET['p'] ?? 1));
            $sessions_per_page = 20;
            
            if (function_exists('get_all_sessions_data_paginated')) {
                $temp_sessions_result = get_all_sessions_data_paginated($country_filter, $ip_filter, $sort, $page, $sessions_per_page);
                $admin_view_data['sessions_result'] = $temp_sessions_result;
                if (is_array($admin_view_data['sessions_result']) && isset($admin_view_data['sessions_result']['sessions'])) {
                    $admin_view_data['sessions'] = $admin_view_data['sessions_result']['sessions'];
                    $admin_view_data['total_sessions'] = $admin_view_data['sessions_result']['total'] ?? 0;
                    $admin_view_data['total_pages'] = $admin_view_data['sessions_result']['total_pages'] ?? 1;
                    $admin_view_data['current_page'] = $page;
                    $admin_view_data['sessions_per_page'] = $sessions_per_page;
                } else {
                    $admin_view_data['sessions'] = [];
                    $admin_view_data['total_sessions'] = 0;
                    $admin_view_data['total_pages'] = 1;
                    $admin_view_data['current_page'] = 1;
                    $admin_view_data['sessions_per_page'] = $sessions_per_page;
                }
            } else {
                
                if (function_exists('get_all_sessions_data')) {
                    $temp_sessions_result = get_all_sessions_data();
                    $admin_view_data['sessions_result'] = $temp_sessions_result;
                    if (is_array($admin_view_data['sessions_result']) && isset($admin_view_data['sessions_result']['sessions'])) {
                        $admin_view_data['sessions'] = $admin_view_data['sessions_result']['sessions'];
                    } else {
                        $admin_view_data['sessions'] = [];
                    }
                } else {
                    $admin_view_data['sessions_result'] = ['success' => false, 'message' => 'Erro: Função get_all_sessions_data() não encontrada. Verifique includes/session.php.', 'sessions' => []];
                    $admin_view_data['sessions'] = [];
                }
                $admin_view_data['total_sessions'] = count($admin_view_data['sessions']);
                $admin_view_data['total_pages'] = 1;
                $admin_view_data['current_page'] = 1;
                $admin_view_data['sessions_per_page'] = $sessions_per_page;
            }
            
            
            $admin_view_data['country_filter'] = $country_filter;
            $admin_view_data['ip_filter'] = $ip_filter;
            $admin_view_data['sort'] = $sort;
            $admin_view_data['filters_active'] = !empty($country_filter) || !empty($ip_filter) || $sort !== 'recent';
            break;

        case 'recommended_products':
            $admin_view_data['page_title'] = "Gerir Produtos Recomendados - " . $admin_page_title;

            // Include the recommended products functions
            require_once __DIR__ . '/includes/recommended_products_functions.php';

            // Get current recommended products
            $admin_view_data['recommended_products'] = get_recommended_products(true);
            $admin_view_data['recommended_count'] = get_recommended_products_count();

            // For adding new products - get search term if provided
            $search_term = $_GET['search'] ?? '';
            $admin_view_data['search_term'] = $search_term;
            $admin_view_data['available_products'] = get_products_available_for_recommendation($search_term, 20);

            break;
    }

    return $admin_view_data;
}

if ($is_admin_logged_in && $is_ajax_request && $_SERVER['REQUEST_METHOD'] === 'GET') {

    
    while (ob_get_level() > 0) {
        ob_end_clean();
    }

    
    if ($section === 'digital_files' && $action === 'get_file_types') {
        define('ADMIN_AJAX_HANDLER', true);
        require_once __DIR__ . '/includes/admin_file_types_handler.php';
        exit;
    }

    ob_start();

    ini_set('display_errors', 0);

    try {

        $admin_view_data = prepare_admin_view_data($section, $action, $item_id, $settings, $current_session_id, $is_admin_logged_in, $admin_page_title);

        if ($is_admin_logged_in) {

            generate_csrf_token();
            $admin_view_data['csrf_token'] = $_SESSION['csrf_token'] ?? '';
        }

        if (isset($admin_view_data['redirect_to'])) {

            ob_end_clean();

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'redirect' => $admin_view_data['redirect_to']
            ]);
            exit;
        }

        
        if ($section === 'products' && ($action === 'list' || empty($action))) {
            if (!isset($admin_view_data['page_title'])) {
                $admin_view_data['page_title'] = "Gerir Produtos - " . ($settings['store_name'] ?? 'eShop');
            }
            
            include_template('backend/products_list.php', $admin_view_data);

            $content = ob_get_clean();
            
            if (strlen($content) < 10 || stripos(trim($content), '<div') !== 0 && stripos(trim($content), '<h1') !== 0  && stripos(trim($content), '<main') !== 0 && stripos(trim($content), '<form') !== 0 && stripos(trim($content), '<table') !== 0 && !empty(trim($content))) {
            }
            if (empty(trim($content)) && $section !== 'logout') {
            }
            $flash_messages = $_SESSION['flash_messages'] ?? [];
            if (!empty($flash_messages)) unset($_SESSION['flash_messages']);

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'content' => $content,
                'section' => $section,
                'action' => $action,
                'page_title' => $admin_view_data['page_title'],
                'flash_messages' => $flash_messages
            ]);
            exit;
        }

        if ($section === 'digital_products' && ($action === 'list' || empty($action))) {

            if (!isset($admin_view_data['digital_products'])) {
                 require_once __DIR__ . '/includes/digital_files_functions.php';
                 $admin_view_data['digital_products'] = get_all_digital_products();
            } else {
            }
            if (!isset($admin_view_data['page_title'])) {
                $admin_view_data['page_title'] = "Produtos Digitais - " . ($settings['store_name'] ?? 'eShop');
            }

            include_template('backend/digital_products_list.php', $admin_view_data);

            $content = ob_get_clean();
            
            if (strlen($content) < 10 || stripos(trim($content), '<div') !== 0 && stripos(trim($content), '<h1') !== 0  && stripos(trim($content), '<main') !== 0 && stripos(trim($content), '<form') !== 0 && stripos(trim($content), '<table') !== 0 && !empty(trim($content))) {
            }
            if (empty(trim($content)) && $section !== 'logout') {
            }
            $flash_messages = $_SESSION['flash_messages'] ?? [];
            if (!empty($flash_messages)) unset($_SESSION['flash_messages']);

            header('Content-Type: application/json');
            echo json_encode([
                'success' => true,
                'content' => $content,
                'section' => $section,
                'action' => $action,
                'page_title' => $admin_view_data['page_title'],
                'flash_messages' => $flash_messages
            ]);
            exit;
        }

        switch ($section) {
            case 'products':
                if (!empty($admin_view_data['template_file'])) {
                include_template('backend/' . $admin_view_data['template_file'], $admin_view_data);
            } elseif ($action === 'edit' || $action === 'new') {
                include_template('backend/product_form.php', $admin_view_data);
            } else {
                include_template('backend/products_list.php', $admin_view_data);
            }
                break;

            case 'attributes':
                $admin_view_data['page_title'] = "Gerir Atributos - " . $admin_page_title;
                if ($action === 'edit' || $action === 'new') {
                    include_template('backend/attribute_form.php', $admin_view_data);
                } elseif ($action === 'values' && isset($_GET['attribute_id'])) {
                    include_template('backend/attribute_values_form.php', $admin_view_data);
                } else {
                    include_template('backend/attributes_list.php', $admin_view_data);
                }
                break;

            case 'orders':
                $admin_view_data['page_title'] = "Gerir Encomendas - " . $admin_page_title;
                if ($action === 'detail') {
                    include_template('backend/order_detail.php', $admin_view_data);
                } else {
                    include_template('backend/orders_list.php', $admin_view_data);
                }
                break;

            case 'pages':
                $admin_view_data['page_title'] = "Gerir Páginas - " . $admin_page_title;
                if ($action === 'edit' || $action === 'new') {
                    include_template('backend/page_form.php', $admin_view_data);
                } else {
                    include_template('backend/pages_list.php', $admin_view_data);
                }
                break;

            case 'page_categories':
                $admin_view_data['page_title'] = "Categorias de Páginas - " . $admin_page_title;
                if ($action === 'edit' || $action === 'new') {
                    include_template('backend/page_category_form.php', $admin_view_data);
                } else {
                    include_template('backend/page_categories_list.php', $admin_view_data);
                }
                break;

            case 'page_placeholders':
                $admin_view_data['page_title'] = "Placeholders de Páginas - " . $admin_page_title;
                if ($action === 'edit' || $action === 'new') {
                    include_template('backend/page_placeholder_form.php', $admin_view_data);
                } else {
                    include_template('backend/page_placeholders_list.php', $admin_view_data);
                }
                break;

            case 'placeholder_links':
                $admin_view_data['page_title'] = "Links de Placeholders - " . $admin_page_title;
                if ($action === 'edit' || $action === 'new') {
                    include_template('backend/placeholder_link_form.php', $admin_view_data);
                } else {
                    include_template('backend/placeholder_links_list.php', $admin_view_data);
                }
                break;

            case 'categories':
                $admin_view_data['page_title'] = "Categorias de Produtos - " . $admin_page_title;
                if ($action === 'edit' || $action === 'new') {
                    include_template('backend/category_form.php', $admin_view_data);
                } else {
                    include_template('backend/categories_list.php', $admin_view_data);
                }
                break;

            case 'settings':
                $admin_view_data['page_title'] = "Configurações - " . $admin_page_title;
                include_template('backend/settings.php', $admin_view_data);
                echo '<script src="public/assets/js/admin-vat-rates.js"></script>';

                break;

            case 'messages':
                $admin_view_data['page_title'] = "Mensagens - " . $admin_page_title;
                include_template('backend/messages.php', $admin_view_data);
                break;

            case 'coupons':
                $admin_view_data['page_title'] = "Gerir Cupões - " . $admin_page_title;
                if ($action === 'edit' || $action === 'new') {
                    include_template('backend/coupon_form.php', $admin_view_data);
                } else {
                    include_template('backend/coupons_list.php', $admin_view_data);
                }
                break;

            case 'payment_methods':
                $admin_view_data['page_title'] = "Métodos de Pagamento - " . $admin_page_title;
                if ($action === 'edit' || $action === 'new') {
                    include_template('backend/payment_method_form.php', $admin_view_data);
                } else {
                    include_template('backend/payment_methods_list.php', $admin_view_data);
                }
                break;

            case 'custom_fields':
                $admin_view_data['page_title'] = "Campos Personalizados - " . $admin_page_title;
                if ($action === 'edit' || $action === 'new') {
                    include_template('backend/custom_field_form.php', $admin_view_data);
                } else {
                    include_template('backend/custom_fields_list.php', $admin_view_data);
                }
                break;

            case 'custom_field_fonts':
                $admin_view_data['page_title'] = "Fontes para Campos Personalizados - " . $admin_page_title;
                if ($action === 'edit' || $action === 'new') {
                    include_template('backend/custom_field_font_form.php', $admin_view_data);
                } else {
                    include_template('backend/custom_field_fonts_list.php', $admin_view_data);
                }
                break;

            case 'vat_rates':
                $admin_view_data['page_title'] = "Taxas de IVA - " . $admin_page_title;

                if ($action === 'add' && $_SERVER['REQUEST_METHOD'] === 'POST') {

                    if (!validate_csrf_token($_POST['csrf_token'] ?? '')) {
                        $response = [
                            'success' => false,
                            'message' => 'Erro de segurança (CSRF). Tente novamente.'
                        ];
                        header('Content-Type: application/json');
                        echo json_encode($response);
                        exit;
                    }

                    $rate = filter_var($_POST['rate'] ?? 0, FILTER_VALIDATE_FLOAT);
                    $description = trim($_POST['description'] ?? '');
                    $is_default = isset($_POST['is_default']) && $_POST['is_default'] == '1';

                    if ($rate === false || $rate < 0 || $rate > 100) {
                        $response = [
                            'success' => false,
                            'message' => 'Taxa de IVA inválida. Deve ser um número entre 0 e 100.'
                        ];
                        header('Content-Type: application/json');
                        echo json_encode($response);
                        exit;
                    }

                    if (empty($description)) {
                        $response = [
                            'success' => false,
                            'message' => 'Descrição da taxa de IVA é obrigatória.'
                        ];
                        header('Content-Type: application/json');
                        echo json_encode($response);
                        exit;
                    }

                    $result = vat_create_rate($rate, $description, $is_default);

                    if ($result) {
                        $response = [
                            'success' => true,
                            'message' => 'Taxa de IVA criada com sucesso.'
                        ];
                    } else {
                        $response = [
                            'success' => false,
                            'message' => 'Erro ao criar taxa de IVA.'
                        ];
                    }

                    header('Content-Type: application/json');
                    echo json_encode($response);
                    exit;
                }
                else if ($action === 'update' && $_SERVER['REQUEST_METHOD'] === 'POST') {

                    if (!validate_csrf_token($_POST['csrf_token'] ?? '')) {
                        $response = [
                            'success' => false,
                            'message' => 'Erro de segurança (CSRF). Tente novamente.'
                        ];
                        header('Content-Type: application/json');
                        echo json_encode($response);
                        exit;
                    }

                    $id = filter_var($_POST['id'] ?? 0, FILTER_VALIDATE_INT);
                    $rate = filter_var($_POST['rate'] ?? 0, FILTER_VALIDATE_FLOAT);
                    $description = trim($_POST['description'] ?? '');

                    if ($id <= 0) {
                        $response = [
                            'success' => false,
                            'message' => 'ID da taxa de IVA inválido.'
                        ];
                        header('Content-Type: application/json');
                        echo json_encode($response);
                        exit;
                    }

                    if ($rate === false || $rate < 0 || $rate > 100) {
                        $response = [
                            'success' => false,
                            'message' => 'Taxa de IVA inválida. Deve ser um número entre 0 e 100.'
                        ];
                        header('Content-Type: application/json');
                        echo json_encode($response);
                        exit;
                    }

                    if (empty($description)) {
                        $response = [
                            'success' => false,
                            'message' => 'Descrição da taxa de IVA é obrigatória.'
                        ];
                        header('Content-Type: application/json');
                        echo json_encode($response);
                        exit;
                    }

                    $current_vat = vat_get_rate($id);
                    $is_default = $current_vat && $current_vat['is_default'] ? true : false;

                    $result = vat_update_rate($id, $rate, $description, $is_default);

                    if ($result) {
                        $response = [
                            'success' => true,
                            'message' => 'Taxa de IVA atualizada com sucesso.'
                        ];
                    } else {
                        $response = [
                            'success' => false,
                            'message' => 'Erro ao atualizar taxa de IVA.'
                        ];
                    }

                    header('Content-Type: application/json');
                    echo json_encode($response);
                    exit;
                }
                else if ($action === 'set_default' && $_SERVER['REQUEST_METHOD'] === 'POST') {

                    if (!validate_csrf_token($_POST['csrf_token'] ?? '')) {
                        $response = [
                            'success' => false,
                            'message' => 'Erro de segurança (CSRF). Tente novamente.'
                        ];
                        header('Content-Type: application/json');
                        echo json_encode($response);
                        exit;
                    }

                    $id = filter_var($_POST['id'] ?? 0, FILTER_VALIDATE_INT);

                    if ($id <= 0) {
                        $response = [
                            'success' => false,
                            'message' => 'ID da taxa de IVA inválido.'
                        ];
                        header('Content-Type: application/json');
                        echo json_encode($response);
                        exit;
                    }

                    $result = vat_set_default_rate($id);

                    if ($result) {
                        $response = [
                            'success' => true,
                            'message' => 'Taxa de IVA definida como padrão com sucesso.'
                        ];
                    } else {
                        $response = [
                            'success' => false,
                            'message' => 'Erro ao definir taxa de IVA como padrão.'
                        ];
                    }

                    header('Content-Type: application/json');
                    echo json_encode($response);
                    exit;
                }
                else if ($action === 'delete' && $_SERVER['REQUEST_METHOD'] === 'POST') {

                    if (!validate_csrf_token($_POST['csrf_token'] ?? '')) {
                        $response = [
                            'success' => false,
                            'message' => 'Erro de segurança (CSRF). Tente novamente.'
                        ];
                        header('Content-Type: application/json');
                        echo json_encode($response);
                        exit;
                    }

                    $id = filter_var($_POST['id'] ?? 0, FILTER_VALIDATE_INT);

                    if ($id <= 0) {
                        $response = [
                            'success' => false,
                            'message' => 'ID da taxa de IVA inválido.'
                        ];
                        header('Content-Type: application/json');
                        echo json_encode($response);
                        exit;
                    }

                    $result = vat_delete_rate($id);

                    if ($result) {
                        $response = [
                            'success' => true,
                            'message' => 'Taxa de IVA eliminada com sucesso.'
                        ];
                    } else {
                        $response = [
                            'success' => false,
                            'message' => 'Erro ao eliminar taxa de IVA. Não é possível eliminar a taxa predefinida se for a única existente.'
                        ];
                    }

                    header('Content-Type: application/json');
                    echo json_encode($response);
                    exit;
                }

                $admin_view_data['vat_rates'] = vat_get_all_rates();
                include_template('backend/vat_rates_list.php', $admin_view_data);
                break;

            case 'blog_categories':
                $admin_view_data['page_title'] = "Categorias do Blog - " . $admin_page_title;
                if ($action === 'edit' || $action === 'new') {
                    include_template('backend/blog_category_form.php', $admin_view_data);
                } else {
                    include_template('backend/blog_categories_list.php', $admin_view_data);
                }
                break;

            case 'blog_posts':
                $admin_view_data['page_title'] = "Posts do Blog - " . $admin_page_title;
                if ($action === 'edit' || $action === 'new') {
                    include_template('backend/blog_post_form.php', $admin_view_data);
                } else {
                    include_template('backend/blog_posts_list.php', $admin_view_data);
                }
                break;

            case 'licenses':
                $admin_view_data['page_title'] = "Gerir Licenças - " . $admin_page_title;

                if ($action === 'generate_code' && ($is_ajax_request || isset($_GET['is_ajax']))) {
                    try {
                        require_once __DIR__ . '/includes/digital_product_functions.php';
                        $license_code = generate_license_code();

                        $exists = db_query(
                            "SELECT 1 FROM licenses WHERE license_code = :code",
                            [':code' => $license_code],
                            true
                        );

                        while ($exists) {
                            $license_code = generate_license_code();
                            $exists = db_query(
                                "SELECT 1 FROM licenses WHERE license_code = :code",
                                [':code' => $license_code],
                                true
                            );
                        }

                        header('Content-Type: application/json');
                        echo json_encode([
                            'success' => true,
                            'license_code' => $license_code
                        ]);
                    } catch (Exception $e) {
                        header('Content-Type: application/json');
                        echo json_encode([
                            'success' => false,
                            'message' => 'Erro ao gerar código de licença: ' . $e->getMessage()
                        ]);
                    }
                    exit;
                }

                if ($action === 'delete' && $item_id) {

                    if (!validate_csrf_token($_GET['csrf_token'] ?? '')) {
                        add_flash_message('Erro de segurança (CSRF). Tente novamente.', 'danger');
                    } else {
                        $license = db_query("SELECT * FROM licenses WHERE id = :id", [':id' => $item_id], true);
                        if ($license) {
                            $result = db_query("DELETE FROM licenses WHERE id = :id", [':id' => $item_id]);
                            if ($result) {
                                add_flash_message('Licença excluída com sucesso.', 'success');
                            } else {
                                add_flash_message('Erro ao excluir licença.', 'danger');
                            }
                        } else {
                            add_flash_message('Licença não encontrada.', 'danger');
                        }
                    }

                    header('Location: admin.php?section=licenses' . ($status_filter ? '&status=' . urlencode($status_filter) : '') . '&' . get_session_id_param());
                    exit;
                } else if ($action === 'edit' || $action === 'new') {
                    include_template('backend/license_form.php', $admin_view_data);
                } else if ($action === 'detail') {
                    include_template('backend/license_detail.php', $admin_view_data);
                } else {
                    include_template('backend/licenses_list.php', $admin_view_data);
                }
                break;

            case 'download_statistics':
                $admin_view_data['page_title'] = "Estatísticas de Downloads - " . $admin_page_title;
                include_template('backend/download_statistics.php', $admin_view_data);
                break;

            case 'digital_files':
                require_once __DIR__ . '/includes/digital_files_functions.php';

                $digital_product_id = isset($_GET['id']) ? (int)$_GET['id'] : (isset($_GET['item_id']) ? (int)$_GET['item_id'] : 0);

                if ($action === 'change_file' && $digital_product_id > 0) {

                    $admin_view_data['item_id'] = $digital_product_id;
                    include_template('backend/change_digital_file.php', $admin_view_data);
                } elseif ($action === 'list' || empty($action)) {

                    include_template('backend/digital_files_list.php', $admin_view_data);
                } elseif ($action === 'manage') {

                    include_template('backend/digital_files_management.php', $admin_view_data);
                } else {

                    include_template('backend/digital_files_list.php', $admin_view_data);
                }
                break;

            case 'maintenance':
                
                if ($action === 'migrate_file_types_tables') {
                    if (!validate_csrf_token($_POST['csrf_token'] ?? '')) {
                        add_flash_message('Erro de segurança (CSRF). Tente novamente.', 'danger');
                    } else {
                        require_once __DIR__ . '/includes/maintenance_functions.php';
                        $result = run_migrate_file_types_tables();
                        if ($result['success']) {
                            add_flash_message($result['message'], 'success');
                        } else {
                            add_flash_message($result['message'], 'danger');
                        }
                    }
                    header('Location: admin.php?section=maintenance&' . get_session_id_param());
                    exit;
                }

                $admin_view_data['page_title'] = "Manutenção - " . $admin_page_title;
                include_template('backend/maintenance_simple.php', $admin_view_data);
                break;

            case 'sitemaps':
                $admin_view_data['page_title'] = "Sitemaps e XMLs - " . $admin_page_title;

                require_once __DIR__ . '/includes/sitemap_functions.php';

                ensure_sitemap_configs_table_exists();

                if ($action === 'create' && isset($_POST['name'])) {

                    $submitted_csrf = $_POST['csrf_token'] ?? '';
                    if (!validate_csrf_token($submitted_csrf)) {
                        add_flash_message('Erro de segurança (CSRF). Tente novamente.', 'danger');
                        header('Location: admin.php?section=sitemaps&' . get_session_id_param());
                        exit;
                    }

                    $sitemap_data = [
                        'name' => $_POST['name'],
                        'type' => $_POST['type'],
                        'output_path' => $_POST['output_path'],
                        'include_products' => isset($_POST['include_products']) ? 1 : 0,
                        'include_blog' => isset($_POST['include_blog']) ? 1 : 0,
                        'include_pages' => isset($_POST['include_pages']) ? 1 : 0,
                        'custom_config_json' => $_POST['custom_config_json'] ?? null,
                        'is_active' => isset($_POST['is_active']) ? 1 : 0
                    ];

                    $result = create_sitemap_config($sitemap_data);
                    if ($result) {
                        add_flash_message('Configuração de sitemap criada com sucesso.', 'success');
                    } else {
                        add_flash_message('Erro ao criar configuração de sitemap.', 'danger');
                    }

                    header('Location: admin.php?section=sitemaps&' . get_session_id_param());
                    exit;
                } elseif ($action === 'update' && isset($_POST['id'])) {

                    $submitted_csrf = $_POST['csrf_token'] ?? '';
                    if (!validate_csrf_token($submitted_csrf)) {
                        add_flash_message('Erro de segurança (CSRF). Tente novamente.', 'danger');
                        header('Location: admin.php?section=sitemaps&' . get_session_id_param());
                        exit;
                    }

                    $sitemap_id = (int)$_POST['id'];
                    $sitemap_data = [
                        'name' => $_POST['name'],
                        'type' => $_POST['type'],
                        'output_path' => $_POST['output_path'],
                        'include_products' => isset($_POST['include_products']) ? 1 : 0,
                        'include_blog' => isset($_POST['include_blog']) ? 1 : 0,
                        'include_pages' => isset($_POST['include_pages']) ? 1 : 0,
                        'custom_config_json' => $_POST['custom_config_json'] ?? null,
                        'is_active' => isset($_POST['is_active']) ? 1 : 0
                    ];

                    $result = update_sitemap_config($sitemap_id, $sitemap_data);
                    if ($result) {
                        add_flash_message('Configuração de sitemap atualizada com sucesso.', 'success');
                    } else {
                        add_flash_message('Erro ao atualizar configuração de sitemap.', 'danger');
                    }

                    header('Location: admin.php?section=sitemaps&' . get_session_id_param());
                    exit;
                } 
                
                elseif ($action === 'edit' && isset($_GET['id'])) {

                    $sitemap_id = (int)$_GET['id'];
                    $sitemap = get_sitemap_config($sitemap_id);
                    if ($sitemap) {
                        $admin_view_data['sitemap'] = $sitemap;
                        include_template('backend/sitemap_form.php', $admin_view_data);
                    } else {
                        add_flash_message('Configuração de sitemap não encontrada.', 'danger');
                        include_template('backend/sitemaps_list.php', $admin_view_data);
                    }
                } elseif ($action === 'new') {

                    include_template('backend/sitemap_form.php', $admin_view_data);
                } else {

                    include_template('backend/sitemaps_list.php', $admin_view_data);
                    }
                    break;

                case 'sessions':

                include_template('backend/sessions_list.php', $admin_view_data);
                break;

            case 'recommended_products':
                include_template('backend/recommended_products_list.php', $admin_view_data);
                break;

            case 'banners':
                $admin_view_data['page_title'] = "Gerenciar Banners - " . $admin_page_title;
                include_template('backend/banners.php', $admin_view_data);
                break;

            case 'dashboard':
            default:
                $admin_view_data['page_title'] = "Dashboard - " . $admin_page_title;
                include_template('backend/dashboard.php', $admin_view_data);
                break;
        }

            $content = ob_get_clean();

            
            if (strlen($content) < 10 || stripos(trim($content), '<div') !== 0 && stripos(trim($content), '<h1') !== 0  && stripos(trim($content), '<main') !== 0 && stripos(trim($content), '<form') !== 0 && stripos(trim($content), '<table') !== 0 && !empty(trim($content))) {
            }
            if (empty(trim($content)) && $section !== 'logout') {
            }

        $flash_messages = [];
        if (isset($_SESSION['flash_messages']) && !empty($_SESSION['flash_messages'])) {
            $flash_messages = $_SESSION['flash_messages'];
            unset($_SESSION['flash_messages']);
        }

        header('Content-Type: application/json');
        echo json_encode([
            'success' => true,
            'content' => $content,
            'section' => $section,
            'action' => $action,
            'page_title' => $admin_view_data['page_title'],
            'flash_messages' => $flash_messages
        ]);
        exit;

    } catch (Exception $e) {

        while (ob_get_level() > 0) {
            ob_end_clean();
        }

        header('Content-Type: application/json');
        $error_message = 'Ocorreu um erro no servidor. Por favor, tente novamente.';
        $debug_info = null;

        echo json_encode([
            'success' => false,
            'message' => $error_message,
            'debug' => $debug_info
        ]);
        exit;
    }

    while (ob_get_level() > 0) {
        ob_end_clean();
    }

    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => 'Ocorreu um erro desconhecido no servidor. Por favor, tente novamente.'
    ]);
    exit;
}

elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'POST' && $section === 'sitemaps' && $action === 'delete') {
    $submitted_csrf = $_POST['csrf_token'] ?? '';
    if (!validate_csrf_token($submitted_csrf)) {
        add_flash_message('Erro de segurança (CSRF). Tente novamente.', 'danger');
    } else {
        require_once __DIR__ . '/includes/sitemap_functions.php';
        $delete_id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);

        if ($delete_id) {
            if (delete_sitemap_config($delete_id)) {
                add_flash_message('Configuração de sitemap/XML eliminada com sucesso.', 'success');
            } else {
                add_flash_message('Erro ao eliminar configuração de sitemap/XML.', 'danger');
            }
        } else {
            add_flash_message('ID de configuração inválido para remoção.', 'warning');
        }
    }
    header('Location: admin.php?section=sitemaps&' . get_session_id_param());
    exit;
}

if ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'POST') {

    if (isset($_SERVER['CONTENT_LENGTH']) && isset($_SERVER['CONTENT_TYPE']) &&
        strpos($_SERVER['CONTENT_TYPE'], 'multipart/form-data') !== false) {
        $max_post_size = ini_get('post_max_size');
        $max_post_bytes = return_bytes($max_post_size);
        $content_length = (int)$_SERVER['CONTENT_LENGTH'];

        if ($content_length > $max_post_bytes) {
            add_flash_message('O tamanho do formulário excede o limite permitido. Reduza o tamanho dos arquivos enviados ou aumente o limite no servidor.', 'danger');
            header('Location: admin.php?section=' . $section . '&action=' . $action . ($item_id ? '&id=' . $item_id : '') . '&' . get_session_id_param());
            exit;
        }
    }

    if ($section === 'digital_files') {
        require_once __DIR__ . '/includes/digital_files_functions.php';

        if (!validate_csrf_token($_POST['csrf_token'] ?? '')) {
            add_flash_message('Erro de segurança (CSRF). Tente novamente.', 'danger');
            header('Location: admin.php?section=digital_files&' . get_session_id_param());
            exit;
        }

        $post_action = $_POST['action'] ?? '';

        if ($post_action === 'upload') {

            if (isset($_FILES['digital_file']) && $_FILES['digital_file']['error'] !== UPLOAD_ERR_NO_FILE) {

                if ($_FILES['digital_file']['error'] !== UPLOAD_ERR_OK) {
                    add_flash_message('Erro ao carregar o arquivo: ' . get_upload_error_message($_FILES['digital_file']['error']), 'danger');
                } else {

                    $max_size = 100 * 1024 * 1024;
                    if ($_FILES['digital_file']['size'] > $max_size) {
                        add_flash_message('O arquivo é muito grande. O tamanho máximo permitido é 100MB.', 'danger');
                    } else {

                        $upload_dir = get_setting('digital_products_directory', '../digital_products');

                        if (strpos($upload_dir, '../') === 0) {
                            $upload_dir = dirname(__DIR__) . '/' . substr($upload_dir, 3);
                        } elseif (strpos($upload_dir, './') === 0) {
                            $upload_dir = __DIR__ . '/' . substr($upload_dir, 2);
                        }

                        if (!file_exists($upload_dir)) {
                            mkdir($upload_dir, 0755, true);
                        }

                        $original_filename = $_FILES['digital_file']['name'];
                        $file_extension = pathinfo($original_filename, PATHINFO_EXTENSION);
                        $new_filename = uniqid('digital_') . '_' . time() . '.' . $file_extension;
                        $absolute_file_path = $upload_dir . '/' . $new_filename;
                        
                        $file_path = '../digital_products/' . $new_filename;

                        if (move_uploaded_file($_FILES['digital_file']['tmp_name'], $absolute_file_path)) {

                            $display_name_input = trim($_POST['new_file_display_name'] ?? '');
                            if (empty($display_name_input)) {
                                $display_name_input = $original_filename;
                            }

                            $short_name_input = trim($_POST['short_name'] ?? '');

                            $file_data = [
                                'original_filename' => $original_filename,
                                'display_name'      => $display_name_input,
                                'short_name'        => $short_name_input,
                                'file_path'         => $file_path,
                                'file_size'         => $_FILES['digital_file']['size'],
                                'file_type'         => $_FILES['digital_file']['type'],
                                'description'       => $_POST['description'] ?? '',
                                'file_types'        => isset($_POST['file_types']) && is_array($_POST['file_types']) ? $_POST['file_types'] : []
                            ];

                            if (create_digital_file($file_data)) {
                                add_flash_message('Arquivo digital carregado com sucesso.', 'success');
                            } else {
                                add_flash_message('Erro ao criar registro do arquivo digital.', 'danger');
                            }
                        } else {
                            add_flash_message('Falha ao mover o arquivo carregado.', 'danger');
                        }
                    }
                }
            } else {
                add_flash_message('Por favor, selecione um arquivo para carregar.', 'danger');
            }

            header('Location: admin.php?section=digital_files&' . get_session_id_param());
            exit;
        } elseif ($post_action === 'delete' && isset($_POST['file_id'])) {

            $file_id = (int)$_POST['file_id'];

            $usage = get_digital_file_usage($file_id);
            if (count($usage) > 0) {
                add_flash_message('Não é possível excluir este arquivo pois está em uso por produtos.', 'danger');
            } else {
                if (delete_digital_file($file_id)) {
                    add_flash_message('Arquivo digital excluído com sucesso.', 'success');
                } else {
                    add_flash_message('Erro ao excluir arquivo digital.', 'danger');
                }
            }

            header('Location: admin.php?section=digital_files&' . get_session_id_param());
            exit;
        } elseif ($post_action === 'update' && isset($_POST['file_id'])) {

            $file_id = (int)$_POST['file_id'];

            $update_data = [];

            
            if (isset($_POST['description'])) {
                $update_data['description'] = $_POST['description'];
            }

            if (isset($_POST['display_name'])) {
                $update_data['display_name'] = $_POST['display_name'];
            }

            if (isset($_POST['short_name'])) {
                $update_data['short_name'] = $_POST['short_name'];
            }

            
            if (isset($_POST['file_types']) && is_array($_POST['file_types'])) {
                $update_data['file_types'] = $_POST['file_types'];
            }

            if (!empty($update_data)) {
                if (update_digital_file($file_id, $update_data)) {
                    add_flash_message('Detalhes do arquivo atualizados com sucesso.', 'success');
                } else {
                    add_flash_message('Erro ao atualizar detalhes do arquivo.', 'danger');
                }
            } else {
                add_flash_message('Nenhum dado fornecido para atualização.', 'warning');
            }

            header('Location: admin.php?section=digital_files&' . get_session_id_param());
            exit;
        } elseif ($post_action === 'update_file_types' && isset($_POST['file_id'])) {
            $file_id = (int)$_POST['file_id'];

            
            $file = get_digital_file_by_id($file_id);
            if (!$file) {
                add_flash_message('Arquivo digital não encontrado.', 'danger');
                header('Location: admin.php?section=digital_files&' . get_session_id_param());
                exit;
            }

            
            $file_type_ids = isset($_POST['file_types']) && is_array($_POST['file_types'])
                ? array_map('intval', $_POST['file_types'])
                : [];

            
            if (update_digital_file_file_types($file_id, $file_type_ids)) {
                add_flash_message('Tipos de arquivo atualizados com sucesso.', 'success');
            } else {
                add_flash_message('Erro ao atualizar tipos de arquivo.', 'danger');
            }

            header('Location: admin.php?section=digital_files&' . get_session_id_param());
            exit;
        } elseif ($post_action === 'upload_new' && isset($_GET['id']) && isset($_GET['item_id'])) {

            $digital_product_id = (int)$_GET['id'];

            if (isset($_FILES['digital_file']) && $_FILES['digital_file']['error'] !== UPLOAD_ERR_NO_FILE) {
                if ($_FILES['digital_file']['error'] !== UPLOAD_ERR_OK) {
                    add_flash_message('Erro ao carregar o arquivo: ' . get_upload_error_message($_FILES['digital_file']['error']), 'danger');
                } else {
                    $max_size = 100 * 1024 * 1024;
                    if ($_FILES['digital_file']['size'] > $max_size) {
                        add_flash_message('O arquivo é muito grande. O tamanho máximo permitido é 100MB.', 'danger');
                    } else {
                        $upload_dir = get_setting('digital_products_directory', '../digital_products');

                        if (strpos($upload_dir, '../') === 0) {
                            $upload_dir = dirname(__DIR__) . '/' . substr($upload_dir, 3);
                        } elseif (strpos($upload_dir, './') === 0) {
                            $upload_dir = __DIR__ . '/' . substr($upload_dir, 2);
                        }

                        if (!file_exists($upload_dir)) {
                            mkdir($upload_dir, 0755, true);
                        }

                        $original_filename = $_FILES['digital_file']['name'];
                        $file_extension = pathinfo($original_filename, PATHINFO_EXTENSION);
                        $new_filename = uniqid('digital_') . '_' . time() . '.' . $file_extension;
                        $absolute_file_path = $upload_dir . '/' . $new_filename;
                        
                        $file_path = '../digital_products/' . $new_filename;

                        if (move_uploaded_file($_FILES['digital_file']['tmp_name'], $absolute_file_path)) {

                            $digital_product = db_query("SELECT * FROM digital_products WHERE id = :id", [':id' => $digital_product_id], true);
                            $product_data = null;
                            if ($digital_product) {
                                $product_data = get_product_by_id($digital_product['product_id']);
                            }

                            $display_name = trim($_POST['new_file_display_name'] ?? '');
                            if (empty($display_name) && $product_data) {
                                $display_name = $product_data['name_pt'];
                            }
                            if (empty($display_name)) {
                                $display_name = $original_filename;
                            }

                            $file_data = [
                                'original_filename' => $original_filename,
                                'display_name' => $display_name,
                                'file_path' => $file_path,
                                'file_size' => $_FILES['digital_file']['size'],
                                'file_type' => $_FILES['digital_file']['type'],
                                'description' => $_POST['new_file_description'] ?? ''
                            ];

                            $file_id = create_digital_file($file_data);
                            if ($file_id) {

                                if (update_digital_product_file($digital_product_id, $file_id)) {

                                    if (!isset($digital_product) || !isset($digital_product['product_id'])) {
                                        $digital_product = db_query("SELECT * FROM digital_products WHERE id = :id", [':id' => $digital_product_id], true);
                                    }

                                    if ($digital_product && isset($digital_product['product_id'])) {
                                        add_flash_message('Arquivo do produto digital atualizado com sucesso.', 'success');
                                        header('Location: admin.php?section=products&action=edit&id=' . $digital_product['product_id'] . '&' . get_session_id_param());
                                        exit;
                                    } else {

                                        add_flash_message('Arquivo do produto digital atualizado com sucesso, mas não foi possível redirecionar para o produto.', 'warning');
                                        header('Location: admin.php?section=digital_products&' . get_session_id_param());
                                        exit;
                                    }
                                } else {
                                    add_flash_message('Erro ao atualizar arquivo do produto digital.', 'danger');
                                }
                            } else {
                                add_flash_message('Erro ao criar registro do arquivo digital.', 'danger');
                            }
                        } else {
                            add_flash_message('Falha ao mover o arquivo carregado.', 'danger');
                        }
                    }
                }
            } else {
                add_flash_message('Por favor, selecione um arquivo para carregar.', 'danger');
            }

            header('Location: admin.php?section=digital_files&action=change_file&id=' . $digital_product_id . '&item_id=' . $digital_product_id . '&' . get_session_id_param());
            exit;
        } elseif ($post_action === 'change_file' && isset($_GET['id']) && isset($_GET['item_id'])) {

            $digital_product_id = (int)$_GET['id'];
            $file_id = (int)($_POST['file_id'] ?? 0);

            if ($file_id <= 0) {
                add_flash_message('Por favor, selecione um arquivo.', 'danger');
            } else {

                $digital_product = db_query("SELECT * FROM digital_products WHERE id = :id", [':id' => $digital_product_id], true);

                if (!$digital_product) {
                    add_flash_message('Produto digital não encontrado.', 'danger');
                    header('Location: admin.php?section=digital_products&' . get_session_id_param());
                    exit;
                }

                if (update_digital_product_file($digital_product_id, $file_id)) {
                    add_flash_message('Arquivo do produto digital atualizado com sucesso.', 'success');
                    header('Location: admin.php?section=products&action=edit&id=' . $digital_product['product_id'] . '&' . get_session_id_param());
                    exit;
                } else {
                    add_flash_message('Erro ao atualizar arquivo do produto digital.', 'danger');
                }
            }

            header('Location: admin.php?section=digital_files&action=change_file&id=' . $digital_product_id . '&item_id=' . $digital_product_id . '&' . get_session_id_param());
            exit;
        }
    }

    $submitted_csrf = $_POST['csrf_token'] ?? '';
    if (!validate_csrf_token($submitted_csrf)) {
        add_flash_message('Erro de segurança (CSRF). Tente novamente.', 'danger');
        header('Location: admin.php?section=dashboard&' . get_session_id_param());
        exit;
    }

    if ($section === 'custom_field_fonts' && ($action === 'create' || $action === 'update')) {
        require_once __DIR__ . '/includes/custom_field_fonts_handler.php';
        $font_id = $action === 'update' ? filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT) : null;
        handle_custom_field_fonts_form($action, $font_id);
        exit;
    }

    if ($section === 'custom_fields' && ($action === 'create' || $action === 'update')) {
        require_once __DIR__ . '/includes/custom_field_handler.php';
        $field_id = $action === 'update' ? filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT) : null;
        handle_custom_field_form($action, $field_id);
        exit;
    }

    if ($section === 'digital_products' && ($action === 'new' || $action === 'edit') && $item_id) {
        require_once __DIR__ . '/includes/digital_files_functions.php';

        $errors = [];
        $file_path = null;
        $file_uploaded = false;
        $existing_file_id = isset($_POST['existing_digital_file_id']) ? (int)$_POST['existing_digital_file_id'] : 0;

        $using_existing_file = ($existing_file_id > 0);

        if ($using_existing_file) {
            $existing_file = get_digital_file_by_id($existing_file_id);
            if (!$existing_file) {
                $errors[] = 'O arquivo digital selecionado não existe.';
                $using_existing_file = false;
            }
        }

        if (!$using_existing_file && isset($_FILES['digital_file']) && $_FILES['digital_file']['error'] !== UPLOAD_ERR_NO_FILE) {
            if ($_FILES['digital_file']['error'] !== UPLOAD_ERR_OK) {
                $errors[] = 'Erro ao carregar o arquivo: ' . get_upload_error_message($_FILES['digital_file']['error']);
            } else {
                $max_size = 100 * 1024 * 1024;
                if ($_FILES['digital_file']['size'] > $max_size) {
                    $errors[] = 'O arquivo é muito grande. O tamanho máximo permitido é 100MB.';
                } else {
                    $upload_dir = get_setting('digital_products_directory', '../digital_products');

                    if (strpos($upload_dir, '../') === 0) {
                        $upload_dir = dirname(__DIR__) . '/' . substr($upload_dir, 3);
                    } elseif (strpos($upload_dir, './') === 0) {
                        $upload_dir = __DIR__ . '/' . substr($upload_dir, 2);
                    }

                    if (!file_exists($upload_dir)) {
                        mkdir($upload_dir, 0755, true);
                    }

                    $original_filename = $_FILES['digital_file']['name'];
                    $file_extension = pathinfo($original_filename, PATHINFO_EXTENSION);
                    $new_filename = uniqid('digital_') . '_' . time() . '.' . $file_extension;
                    $absolute_file_path = $upload_dir . '/' . $new_filename;
                    
                    $file_path = '../digital_products/' . $new_filename;

                    if (move_uploaded_file($_FILES['digital_file']['tmp_name'], $absolute_file_path)) {
                        $file_uploaded = true;
                    } else {
                        $errors[] = 'Falha ao mover o arquivo carregado.';
                    }
                }
            }
        } elseif ($action === 'new' && !$using_existing_file) {

            $digital_product = get_digital_product_by_product_id($item_id);
            if (!$digital_product) {

                $errors[] = 'Por favor, carregue um arquivo ou selecione um arquivo existente para o produto digital.';
            }
        }

        $expiry_days = filter_input(INPUT_POST, 'expiry_days', FILTER_VALIDATE_INT);
        if ($expiry_days === false || $expiry_days < 0) {
            $errors[] = 'Dias de expiração inválidos.';
        }

        $download_limit = filter_input(INPUT_POST, 'download_limit', FILTER_VALIDATE_INT);
        if ($download_limit === false || $download_limit < 0) {
            $errors[] = 'Limite de downloads inválido.';
        }

        $file_types = $_POST['file_types'] ?? [];
        if (empty($file_types)) {
            $errors[] = 'Selecione pelo menos um tipo de arquivo.';
        }

        if (empty($errors)) {

            $data = [
                'product_id' => $item_id,
                'expiry_days' => $expiry_days,
                'download_limit' => $download_limit,
                'file_types' => $file_types
            ];

            if ($using_existing_file) {

                $data['digital_file_id'] = $existing_file_id;

                $existing_file_details = get_digital_file_by_id($existing_file_id);
                if ($existing_file_details) {
                } else {
                    $errors[] = 'Arquivo digital selecionado não encontrado.';
                }
            } else if ($file_uploaded) {

                $display_name = trim($_POST['digital_file_display_name'] ?? '');
                if (empty($display_name)) {
                    $product_data = get_product_by_id($item_id);
                    $display_name = $product_data ? $product_data['name_pt'] : $original_filename;
                }

                $new_file_data = [
                    'original_filename' => $original_filename,
                    'display_name' => $display_name,
                    'short_name' => $display_name,
                    'file_path' => $file_path,
                    'file_size' => $_FILES['digital_file']['size'],
                    'file_type' => $_FILES['digital_file']['type'],
                    'description' => ''
                ];

                $new_file_id = create_digital_file($new_file_data);
                if ($new_file_id) {
                    $data['digital_file_id'] = $new_file_id;
                } else {
                    $errors[] = 'Erro ao criar registro do arquivo digital.';
                }
            }

            $digital_product = get_digital_product_by_product_id($item_id);

            $pdo = get_db_connection();
            if (!$pdo) {
                add_flash_message('Erro de conexão com o banco de dados.', 'danger');
                header('Location: admin.php?section=digital_products&action=' . $action . '&id=' . $item_id . '&' . get_session_id_param());
                exit;
            }

            try {
                
                if (!$pdo->inTransaction()) {
                    $pdo->beginTransaction();
                }

                if ($digital_product) {

                    if (!update_digital_product($digital_product['id'], $data)) {
                        throw new Exception('Erro ao atualizar o produto digital.');
                    }

                    $result = db_query("UPDATE products SET product_type = 'digital' WHERE id = :id", [':id' => $item_id]);
                    if ($result === false) {
                        throw new Exception('Erro ao atualizar o tipo de produto.');
                    }

                    
                    if ($pdo->inTransaction()) {
                        $pdo->commit();
                    }
                    add_flash_message('Produto digital atualizado com sucesso.', 'success');
                    header('Location: admin.php?section=products&action=edit&id=' . $item_id . '&' . get_session_id_param());
                    exit;
                } else {

                    $digital_product_id = create_digital_product($data);
                    if (!$digital_product_id) {
                        throw new Exception('Erro ao criar o produto digital.');
                    }

                    $result = db_query("UPDATE products SET product_type = 'digital' WHERE id = :id", [':id' => $item_id]);
                    if ($result === false) {
                        throw new Exception('Erro ao atualizar o tipo de produto.');
                    }

                    
                    if ($pdo->inTransaction()) {
                        $pdo->commit();
                    }
                    add_flash_message('Produto digital criado com sucesso.', 'success');
                    header('Location: admin.php?section=products&action=edit&id=' . $item_id . '&' . get_session_id_param());
                    exit;
                }
            } catch (Exception $e) {

                if ($pdo->inTransaction()) {
                    $pdo->rollBack();
                }

                add_flash_message('Erro ao guardar o produto digital: ' . $e->getMessage(), 'danger');
                header('Location: admin.php?section=digital_products&action=' . $action . '&id=' . $item_id . '&' . get_session_id_param());
                exit;
            }
        } else {

            foreach ($errors as $error) {
                add_flash_message($error, 'danger');
            }
        }

        header('Location: admin.php?section=digital_products&action=' . $action . '&id=' . $item_id . '&' . get_session_id_param());
        exit;
    }

    if ($section === 'sitemaps' && ($action === 'create' || $action === 'update')) {
        require_once __DIR__ . '/includes/sitemap_functions.php';

        if ($action === 'create') {

            $sitemap_data = [
                'name' => $_POST['name'] ?? '',
                'type' => $_POST['type'] ?? 'sitemap',
                'output_path' => $_POST['output_path'] ?? '',
                'include_products' => isset($_POST['include_products']) ? 1 : 0,
                'include_blog' => isset($_POST['include_blog']) ? 1 : 0,
                'include_pages' => isset($_POST['include_pages']) ? 1 : 0,
                'custom_config_json' => $_POST['custom_config_json'] ?? null,
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'include_regular_products' => isset($_POST['include_regular_products']) ? 1 : 0,
                'include_variation_products' => isset($_POST['include_variation_products']) ? 1 : 0,
                'include_digital_products' => isset($_POST['include_digital_products']) ? 1 : 0
            ];

            $result = create_sitemap_config($sitemap_data);
            if ($result) {
                add_flash_message('Configuração de sitemap criada com sucesso.', 'success');
            } else {
                add_flash_message('Erro ao criar configuração de sitemap.', 'danger');
            }
        } elseif ($action === 'update' && isset($_POST['id'])) {

            $sitemap_id = (int)$_POST['id'];
            $sitemap_data = [
                'name' => $_POST['name'] ?? '',
                'type' => $_POST['type'] ?? 'sitemap',
                'output_path' => $_POST['output_path'] ?? '',
                'include_products' => isset($_POST['include_products']) ? 1 : 0,
                'include_blog' => isset($_POST['include_blog']) ? 1 : 0,
                'include_pages' => isset($_POST['include_pages']) ? 1 : 0,
                'custom_config_json' => $_POST['custom_config_json'] ?? null,
                'is_active' => isset($_POST['is_active']) ? 1 : 0,
                'include_regular_products' => isset($_POST['include_regular_products']) ? 1 : 0,
                'include_variation_products' => isset($_POST['include_variation_products']) ? 1 : 0,
                'include_digital_products' => isset($_POST['include_digital_products']) ? 1 : 0
            ];

            $result = update_sitemap_config($sitemap_id, $sitemap_data);
            if ($result) {
                add_flash_message('Configuração de sitemap atualizada com sucesso.', 'success');
            } else {
                add_flash_message('Erro ao atualizar configuração de sitemap.', 'danger');
            }
        }

        header('Location: admin.php?section=sitemaps&' . get_session_id_param());
        exit;
    }

    if ($section === 'settings') {
        require_once __DIR__ . '/includes/settings_handler.php';

        $result = handle_settings_update($_POST, $_FILES);

        if (!empty($result['errors'])) {
            foreach ($result['errors'] as $error_key => $error_message) {

                add_flash_message($error_message, 'danger');
            }
        }
        if (!empty($result['messages'])) {

             $message_type = empty($result['errors']) ? 'success' : 'info';
            foreach ($result['messages'] as $message) {
                add_flash_message($message, $message_type);
            }
        }

        header('Location: admin.php?section=settings&' . get_session_id_param());
        exit;
    }

    elseif ($section === 'products' && ($action === 'new' || ($action === 'edit' && $item_id))) {
        $created_at_input = trim($_POST['created_at'] ?? '');
        $formatted_created_at = null;
        if (!empty($created_at_input)) {
            try {
                
                $dt = new DateTime($created_at_input);
                $formatted_created_at = $dt->format('Y-m-d H:i:s');
            } catch (Exception $e) {
                add_flash_message("Formato de data de criação inválido ('" . sanitize_input($created_at_input) . "'). A data de criação não foi alterada.", 'warning');
            }
        }

        $name_pt = trim($_POST['name_pt'] ?? '');
        $slug = generate_slug(trim($_POST['slug'] ?? '') ?: $name_pt);
        $description_pt = trim($_POST['description_pt'] ?? '');
        $base_price = filter_var($_POST['base_price'] ?? 0, FILTER_VALIDATE_FLOAT);
        $is_active = isset($_POST['is_active']) && $_POST['is_active'] == '1' ? 1 : 0;
        $category_id = filter_input(INPUT_POST, 'category_id', FILTER_VALIDATE_INT);
        $category_id = ($category_id && $category_id > 0) ? $category_id : null;

        $seo_title = trim($_POST['seo_title'] ?? '');
        $seo_description = trim($_POST['seo_description'] ?? '');
        $seo_keywords = trim($_POST['seo_keywords'] ?? '');
        $og_title = trim($_POST['og_title'] ?? '');
        $og_description = trim($_POST['og_description'] ?? '');
        $og_image = trim($_POST['og_image'] ?? '');
        $twitter_card = trim($_POST['twitter_card'] ?? 'summary_large_image');
        $twitter_title = trim($_POST['twitter_title'] ?? '');
        $twitter_description = trim($_POST['twitter_description'] ?? '');
        $twitter_image = trim($_POST['twitter_image'] ?? '');

        $product_type = $_POST['product_type'] ?? 'regular';
        $is_simple_product = ($product_type === 'regular');
        $is_digital_product = ($product_type === 'digital');

        $simple_sku = '';
        $simple_stock = 0;
        if ($is_simple_product) {
            $simple_sku = trim($_POST['simple_sku'] ?? '');
            $simple_stock = filter_var($_POST['simple_stock'] ?? 0, FILTER_VALIDATE_INT);
            if ($simple_stock === false) $simple_stock = 0;
        }

        $digital_sku = '';
        $digital_expiry_days = 0;
        $digital_download_limit = 0;
        $digital_file_types = [];
        if ($is_digital_product) {
            $digital_sku = trim($_POST['digital_sku'] ?? '');
            $digital_expiry_days = filter_var($_POST['digital_expiry_days'] ?? get_setting('digital_download_expiry_days', 5), FILTER_VALIDATE_INT);
            if ($digital_expiry_days === false) $digital_expiry_days = get_setting('digital_download_expiry_days', 5);

            $digital_download_limit = filter_var($_POST['digital_download_limit'] ?? get_setting('digital_download_limit', 3), FILTER_VALIDATE_INT);
            if ($digital_download_limit === false) $digital_download_limit = get_setting('digital_download_limit', 3);

            $digital_file_types = $_POST['digital_file_types'] ?? [];
        }

        $vat_rate_id = filter_input(INPUT_POST, 'vat_rate_id', FILTER_VALIDATE_INT);

        require_once __DIR__ . '/includes/vat_functions.php';

        if (!$vat_rate_id) {
            $default_vat_rate = get_default_vat_rate();
            $vat_rate_id = $default_vat_rate ? $default_vat_rate['id'] : null;
        }

        $errors = [];
        if (empty($name_pt)) $errors[] = "Nome do produto é obrigatório.";
        if (empty($slug)) $errors[] = "Slug é obrigatório.";
        if ($base_price === false || $base_price < 0) $errors[] = "Preço base inválido.";

        if ($is_simple_product) {

            if (empty($simple_sku)) {
                $simple_sku = generate_unique_sku($name_pt, true, null, $action === 'edit' ? $item_id : null);
            } elseif (strlen($simple_sku) != 10) {
                $errors[] = "SKU para produtos simples deve ter exatamente 10 caracteres.";
            }

            $sku_check_sql = "SELECT id FROM products WHERE sku = :sku";
            $sku_check_params = [':sku' => $simple_sku];
            if ($action === 'edit') {
                $sku_check_sql .= " AND id != :id";
                $sku_check_params[':id'] = $item_id;
            }
            $existing_sku = db_query($sku_check_sql, $sku_check_params, true);
            if ($existing_sku) $errors[] = "O SKU '$simple_sku' já está em uso. Escolha outro.";

            if ($simple_stock < 0) $errors[] = "Stock não pode ser negativo.";
        }

        if ($is_digital_product) {

            if (empty($digital_sku)) {
                $digital_sku = generate_unique_sku($name_pt, false, null, $action === 'edit' ? $item_id : null);
            } elseif (strlen($digital_sku) != 10) {
                $errors[] = "SKU para produtos digitais deve ter exatamente 10 caracteres.";
            }

            $sku_check_sql = "SELECT id FROM products WHERE sku = :sku";
            $sku_check_params = [':sku' => $digital_sku];
            if ($action === 'edit') {
                $sku_check_sql .= " AND id != :id";
                $sku_check_params[':id'] = $item_id;
            }
            $existing_sku = db_query($sku_check_sql, $sku_check_params, true);
            if ($existing_sku) $errors[] = "O SKU '$digital_sku' já está em uso. Escolha outro.";

            if ($digital_expiry_days < 1) {
                $errors[] = "Dias de expiração deve ser maior que zero.";
            }

            if ($digital_download_limit < 1) {
                $errors[] = "Limite de downloads deve ser maior que zero.";
            }
        }

        $slug_check_sql = "SELECT id FROM products WHERE slug = :slug";
        $slug_check_params = [':slug' => $slug];
        if ($action === 'edit') {
            $slug_check_sql .= " AND id != :id";
            $slug_check_params[':id'] = $item_id;
        }
        $existing_slug = db_query($slug_check_sql, $slug_check_params, true);
        if ($existing_slug) $errors[] = "O slug '$slug' já está em uso. Escolha outro.";

        if (!empty($errors)) {
            foreach ($errors as $error) add_flash_message($error, 'danger');
            $_SESSION['form_data'] = $_POST;
            $redirect_url = 'admin.php?section=products&action=' . $action;
            if ($item_id) $redirect_url .= '&id=' . $item_id;
            header('Location: ' . $redirect_url . '&' . get_session_id_param());
            exit;
        }

        $pdo = get_db_connection();
        if (!$pdo) {
             add_flash_message('Erro crítico: Falha na ligação à base de dados.', 'danger');
             header('Location: admin.php?section=products&' . get_session_id_param());
             exit;
        }
        $product_id = $item_id;

        
        if (!defined('PRESERVE_FILE_TYPE_ASSOCIATIONS')) {
            define('PRESERVE_FILE_TYPE_ASSOCIATIONS', true);
        }

        try {
            $pdo->beginTransaction();

            if ($action === 'new') {

                if ($is_simple_product) {
                    $sql = "INSERT INTO products (name_pt, slug, description_pt, base_price, is_active, vat_rate_id, sku, stock, product_type,
                            seo_title, seo_description, seo_keywords, og_title, og_description, og_image,
                            twitter_card, twitter_title, twitter_description, twitter_image,
                            created_at, updated_at)
                            VALUES (:name_pt, :slug, :description_pt, :base_price, :is_active, :vat_rate_id, :sku, :stock, :product_type,
                            :seo_title, :seo_description, :seo_keywords, :og_title, :og_description, :og_image,
                            :twitter_card, :twitter_title, :twitter_description, :twitter_image,
                            :created_at, datetime('now', 'localtime'))";
                    $params = [
                        ':name_pt' => $name_pt,
                        ':slug' => $slug,
                        ':description_pt' => $description_pt,
                        ':base_price' => $base_price,
                        ':is_active' => $is_active,
                        ':vat_rate_id' => $vat_rate_id,
                        ':sku' => $simple_sku,
                        ':stock' => $simple_stock,
                        ':product_type' => 'regular',
                        ':seo_title' => $seo_title,
                        ':seo_description' => $seo_description,
                        ':seo_keywords' => $seo_keywords,
                        ':og_title' => $og_title,
                        ':og_description' => $og_description,
                        ':og_image' => $og_image,
                        ':twitter_card' => $twitter_card,
                        ':twitter_title' => $twitter_title,
                        ':twitter_description' => $twitter_description,
                        ':twitter_image' => $twitter_image,
                        ':created_at' => $formatted_created_at 
                    ];
                } elseif ($is_digital_product) {
                    $sql = "INSERT INTO products (name_pt, slug, description_pt, base_price, is_active, vat_rate_id, sku, stock, product_type,
                            seo_title, seo_description, seo_keywords, og_title, og_description, og_image,
                            twitter_card, twitter_title, twitter_description, twitter_image,
                            created_at, updated_at)
                            VALUES (:name_pt, :slug, :description_pt, :base_price, :is_active, :vat_rate_id, :sku, :stock, :product_type,
                            :seo_title, :seo_description, :seo_keywords, :og_title, :og_description, :og_image,
                            :twitter_card, :twitter_title, :twitter_description, :twitter_image,
                            datetime('now', 'localtime'), datetime('now', 'localtime'))";
                    $params = [
                        ':name_pt' => $name_pt,
                        ':slug' => $slug,
                        ':description_pt' => $description_pt,
                        ':base_price' => $base_price,
                        ':is_active' => $is_active,
                        ':vat_rate_id' => $vat_rate_id,
                        ':sku' => $digital_sku,
                        ':stock' => 999999,
                        ':product_type' => 'digital',
                        ':seo_title' => $seo_title,
                        ':seo_description' => $seo_description,
                        ':seo_keywords' => $seo_keywords,
                        ':og_title' => $og_title,
                        ':og_description' => $og_description,
                        ':og_image' => $og_image,
                        ':twitter_card' => $twitter_card,
                        ':twitter_title' => $twitter_title,
                        ':twitter_description' => $twitter_description,
                        ':twitter_image' => $twitter_image
                    ];
                } else {
                    $sql = "INSERT INTO products (name_pt, slug, description_pt, base_price, is_active, vat_rate_id, product_type,
                            seo_title, seo_description, seo_keywords, og_title, og_description, og_image,
                            twitter_card, twitter_title, twitter_description, twitter_image,
                            created_at, updated_at)
                            VALUES (:name_pt, :slug, :description_pt, :base_price, :is_active, :vat_rate_id, :product_type,
                            :seo_title, :seo_description, :seo_keywords, :og_title, :og_description, :og_image,
                            :twitter_card, :twitter_title, :twitter_description, :twitter_image,
                            datetime('now', 'localtime'), datetime('now', 'localtime'))";
                    $params = [
                        ':name_pt' => $name_pt,
                        ':slug' => $slug,
                        ':description_pt' => $description_pt,
                        ':base_price' => $base_price,
                        ':is_active' => $is_active,
                        ':vat_rate_id' => $vat_rate_id,
                        ':product_type' => 'variation',
                        ':seo_title' => $seo_title,
                        ':seo_description' => $seo_description,
                        ':seo_keywords' => $seo_keywords,
                        ':og_title' => $og_title,
                        ':og_description' => $og_description,
                        ':og_image' => $og_image,
                        ':twitter_card' => $twitter_card,
                        ':twitter_title' => $twitter_title,
                        ':twitter_description' => $twitter_description,
                        ':twitter_image' => $twitter_image
                    ];
                }
                db_query($sql, $params);
                $product_id = $pdo->lastInsertId();
            } else {

                if ($is_simple_product) {
                    $update_fields_sql = "name_pt = :name_pt, slug = :slug, description_pt = :description_pt,
                            base_price = :base_price, is_active = :is_active, vat_rate_id = :vat_rate_id,
                            sku = :sku, stock = :stock, product_type = :product_type,
                            seo_title = :seo_title, seo_description = :seo_description, seo_keywords = :seo_keywords,
                            og_title = :og_title, og_description = :og_description, og_image = :og_image,
                            twitter_card = :twitter_card, twitter_title = :twitter_title,
                            twitter_description = :twitter_description, twitter_image = :twitter_image,
                            updated_at = datetime('now', 'localtime')";
                    $params = [
                        ':name_pt' => $name_pt,
                        ':slug' => $slug,
                        ':description_pt' => $description_pt,
                        ':base_price' => $base_price,
                        ':is_active' => $is_active,
                        ':vat_rate_id' => $vat_rate_id,
                        ':sku' => $simple_sku,
                        ':stock' => $simple_stock,
                        ':product_type' => 'regular',
                        ':seo_title' => $seo_title,
                        ':seo_description' => $seo_description,
                        ':seo_keywords' => $seo_keywords,
                        ':og_title' => $og_title,
                        ':og_description' => $og_description,
                        ':og_image' => $og_image,
                        ':twitter_card' => $twitter_card,
                        ':twitter_title' => $twitter_title,
                        ':twitter_description' => $twitter_description,
                        ':twitter_image' => $twitter_image,
                        ':id' => $product_id
                    ];
                    if ($formatted_created_at) {
                        $update_fields_sql .= ", created_at = :created_at";
                        $params[':created_at'] = $formatted_created_at;
                    }
                    $sql = "UPDATE products SET " . $update_fields_sql . " WHERE id = :id";
                } elseif ($is_digital_product) {
                    $update_fields_sql = "name_pt = :name_pt, slug = :slug, description_pt = :description_pt,
                            base_price = :base_price, is_active = :is_active, vat_rate_id = :vat_rate_id,
                            sku = :sku, stock = :stock, product_type = :product_type,
                            seo_title = :seo_title, seo_description = :seo_description, seo_keywords = :seo_keywords,
                            og_title = :og_title, og_description = :og_description, og_image = :og_image,
                            twitter_card = :twitter_card, twitter_title = :twitter_title,
                            twitter_description = :twitter_description, twitter_image = :twitter_image,
                            updated_at = datetime('now', 'localtime')";
                    $params = [
                        ':name_pt' => $name_pt,
                        ':slug' => $slug,
                        ':description_pt' => $description_pt,
                        ':base_price' => $base_price,
                        ':is_active' => $is_active,
                        ':vat_rate_id' => $vat_rate_id,
                        ':sku' => $digital_sku,
                        ':stock' => 999999, 
                        ':product_type' => 'digital',
                        ':seo_title' => $seo_title,
                        ':seo_description' => $seo_description,
                        ':seo_keywords' => $seo_keywords,
                        ':og_title' => $og_title,
                        ':og_description' => $og_description,
                        ':og_image' => $og_image,
                        ':twitter_card' => $twitter_card,
                        ':twitter_title' => $twitter_title,
                        ':twitter_description' => $twitter_description,
                        ':twitter_image' => $twitter_image,
                        ':id' => $product_id
                    ];
                    if ($formatted_created_at) {
                        $update_fields_sql .= ", created_at = :created_at";
                        $params[':created_at'] = $formatted_created_at;
                    }
                    $sql = "UPDATE products SET " . $update_fields_sql . " WHERE id = :id";
                } else { 
                    $update_fields_sql = "name_pt = :name_pt, slug = :slug, description_pt = :description_pt,
                            base_price = :base_price, is_active = :is_active, vat_rate_id = :vat_rate_id,
                            sku = NULL, stock = NULL, product_type = :product_type,
                            seo_title = :seo_title, seo_description = :seo_description, seo_keywords = :seo_keywords,
                            og_title = :og_title, og_description = :og_description, og_image = :og_image,
                            twitter_card = :twitter_card, twitter_title = :twitter_title,
                            twitter_description = :twitter_description, twitter_image = :twitter_image,
                            updated_at = datetime('now', 'localtime')";
                    $params = [
                        ':name_pt' => $name_pt,
                        ':slug' => $slug,
                        ':description_pt' => $description_pt,
                        ':base_price' => $base_price,
                        ':is_active' => $is_active,
                        ':vat_rate_id' => $vat_rate_id,
                        ':product_type' => 'variation',
                        ':seo_title' => $seo_title,
                        ':seo_description' => $seo_description,
                        ':seo_keywords' => $seo_keywords,
                        ':og_title' => $og_title,
                        ':og_description' => $og_description,
                        ':og_image' => $og_image,
                        ':twitter_card' => $twitter_card,
                        ':twitter_title' => $twitter_title,
                        ':twitter_description' => $twitter_description,
                        ':twitter_image' => $twitter_image,
                        ':id' => $product_id
                    ];
                    if ($formatted_created_at) {
                        $update_fields_sql .= ", created_at = :created_at";
                        $params[':created_at'] = $formatted_created_at;
                    }
                    $sql = "UPDATE products SET " . $update_fields_sql . " WHERE id = :id";
                }
                db_query($sql, $params);
            }

           if ($is_digital_product) {
               require_once __DIR__ . '/includes/digital_product_functions.php';

               $existing_digital_product = get_digital_product_by_product_id($product_id);

               $newly_uploaded_digital_file_id = null;
               $upload_success = false;

               if (isset($_FILES['digital_file']) && $_FILES['digital_file']['error'] === UPLOAD_ERR_OK) {
                   $digital_products_dir = get_setting('digital_products_directory', '../digital_products');
                   if (!is_dir($digital_products_dir)) {
                       mkdir($digital_products_dir, 0755, true);
                   }

                   $original_name = $_FILES['digital_file']['name'];
                   $file_extension = pathinfo($original_name, PATHINFO_EXTENSION);
                   $safe_original_name = preg_replace('/[^a-zA-Z0-9_\-\.]/', '', pathinfo($original_name, PATHINFO_FILENAME));
                   $unique_filename = 'digital_' . $product_id . '_' . time() . '_' . $safe_original_name . '.' . $file_extension;
                   $destination = $digital_products_dir . '/' . $unique_filename;

                   if (move_uploaded_file($_FILES['digital_file']['tmp_name'], $destination)) {
                       $upload_success = true;

                       $df_display_name_from_post = trim($_POST['digital_file_display_name'] ?? '');
                       $df_description_from_post = '';

                       $final_df_display_name = $df_display_name_from_post;
                       if (empty($final_df_display_name)) {
                           $product_details_for_df = db_query("SELECT name_pt FROM products WHERE id = :pid", [':pid' => $product_id], true);
                           $final_df_display_name = $product_details_for_df ? $product_details_for_df['name_pt'] : $original_name;
                       }
                       if (empty($final_df_display_name)) {
                           $final_df_display_name = $original_name;
                       }

                       $new_digital_file_id = create_digital_file([
                           'original_filename' => $original_name,
                           'display_name' => $final_df_display_name,
                           'file_path' => $destination,
                           'file_size' => $_FILES['digital_file']['size'],
                           'file_type' => $_FILES['digital_file']['type'],
                           'description' => $df_description_from_post
                       ]);

                       if ($new_digital_file_id) {
                           $newly_uploaded_digital_file_id = $new_digital_file_id;

                           if ($existing_digital_product && !empty($existing_digital_product['digital_file_id'])) {
                               $old_file_info = get_digital_file_by_id($existing_digital_product['digital_file_id']);
                               if ($old_file_info && file_exists($old_file_info['file_path'])) {
                                   @unlink($old_file_info['file_path']);

                               }
                           }
                       } else {
                           add_flash_message("Erro ao criar registo para o arquivo digital.", 'danger');
                           $upload_success = false;
                       }
                   } else {
                       add_flash_message("Erro ao fazer upload do arquivo digital (falha ao mover).", 'danger');
                   }
               } elseif (isset($_FILES['digital_file']) && $_FILES['digital_file']['error'] !== UPLOAD_ERR_NO_FILE && ($action === 'new' || ($action === 'edit' && !$existing_digital_product))) {
                   add_flash_message("Erro no upload do arquivo digital: " . get_upload_error_message($_FILES['digital_file']['error']), 'danger');
               }

               $digital_file_types = $_POST['digital_file_types'] ?? [];

               $current_digital_file_id_to_use = $existing_digital_product['digital_file_id'] ?? null;
               if ($newly_uploaded_digital_file_id) {
                   $current_digital_file_id_to_use = $newly_uploaded_digital_file_id;
               }

               if (!$current_digital_file_id_to_use && ($action === 'new' || ($action === 'edit' && !$existing_digital_product && !$upload_success))) {
                    add_flash_message("É necessário um arquivo digital para este produto.", 'danger');

                    header('Location: admin.php?section=products&action=' . $action . '&id=' . $product_id . '&' . get_session_id_param());
                    exit;
               }

               if ($existing_digital_product) {
                   
                   require_once __DIR__ . '/includes/digital_files_functions.php';
                   $existing_file_type_ids = [];

                   if ($current_digital_file_id_to_use) {
                       $existing_file_type_ids = get_digital_file_file_type_ids((int)$current_digital_file_id_to_use);
                   }

                   
                   
                   if (empty($digital_file_types) && !empty($existing_file_type_ids)) {
                       $digital_file_types = $existing_file_type_ids;
                   } else {
                   }

                   $digital_product_data = [
                       'expiry_days' => $digital_expiry_days,
                       'download_limit' => $digital_download_limit,
                       'file_types' => $digital_file_types,
                       'digital_file_id' => $current_digital_file_id_to_use
                   ];

                   if (update_digital_product($existing_digital_product['id'], $digital_product_data)) {
                       
                   } else {
                       add_flash_message("Erro ao atualizar produto digital.", 'danger');
                   }
               } else {

                   if ($current_digital_file_id_to_use) {
                       
                       require_once __DIR__ . '/includes/digital_files_functions.php';
                       $existing_file_type_ids = [];

                       if ($current_digital_file_id_to_use) {
                           $existing_file_type_ids = get_digital_file_file_type_ids((int)$current_digital_file_id_to_use);
                       }

                       
                       
                       if (empty($digital_file_types) && !empty($existing_file_type_ids)) {
                           $digital_file_types = $existing_file_type_ids;
                       } else {
                       }

                       $digital_product_data = [
                           'product_id' => $product_id,
                           'digital_file_id' => $current_digital_file_id_to_use,
                           'expiry_days' => $digital_expiry_days,
                           'download_limit' => $digital_download_limit,
                           'file_types' => $digital_file_types
                       ];

                       $new_dp_id = create_digital_product($digital_product_data);
                       if (!$new_dp_id) {
                           add_flash_message("Erro ao criar associação do produto digital.", 'danger');
                       }
                   } elseif ($action === 'new' || $action === 'edit') {
                        add_flash_message("Erro: Nenhum arquivo digital foi enviado ou selecionado para criar o produto digital.", 'danger');
                   }
               }
           }

           $selected_category_ids = $_POST['categories'] ?? [];
           $sanitized_category_ids = [];
           if (is_array($selected_category_ids)) {
               foreach ($selected_category_ids as $cat_id) {
                   $valid_id = filter_var($cat_id, FILTER_VALIDATE_INT);
                   if ($valid_id && $valid_id > 0) {
                       $sanitized_category_ids[] = $valid_id;
                   }
               }
           }

           db_query("DELETE FROM product_categories WHERE product_id = :pid", [':pid' => $product_id]);

           if (!empty($sanitized_category_ids)) {
               $cat_link_sql = "INSERT INTO product_categories (product_id, category_id) VALUES (:pid, :cid)";
               foreach ($sanitized_category_ids as $category_id_to_link) {
                   db_query($cat_link_sql, [':pid' => $product_id, ':cid' => $category_id_to_link]);
               }
           }

            if (!$is_simple_product) {
                $variations_data = $_POST['variations'] ?? [];

                $variation_attribute_sets = [];
                $duplicate_variations = [];

                foreach ($variations_data as $variation_key => $variation) {
                    if (isset($variation['delete']) && $variation['delete'] == '1') {
                        continue;
                    }

                    if (isset($variation['attributes']) && is_array($variation['attributes'])) {

                        $attribute_ids = array_keys($variation['attributes']);
                        sort($attribute_ids);
                        $attribute_key = implode('-', $attribute_ids);

                        if (isset($variation_attribute_sets[$attribute_key])) {

                            $duplicate_variations[$variation_key] = true;
                        } else {
                            $variation_attribute_sets[$attribute_key] = $variation_key;
                        }
                    }
                }

                foreach ($variations_data as $variation_key => $variation) {

                    if (isset($duplicate_variations[$variation_key])) {
                        continue;
                    }
                    $is_new = isset($variation['is_new']) && $variation['is_new'] == '1';
                    $variation_id = !$is_new ? (int)$variation_key : null;
                    $delete_variation = isset($variation['delete']) && $variation['delete'] == '1';

                    if ($delete_variation && $variation_id) {
                        db_query("DELETE FROM variation_values WHERE variation_id = :vid", [':vid' => $variation_id]);
                        db_query("DELETE FROM product_variations WHERE id = :vid", [':vid' => $variation_id]);
                        continue;
                    }

                    $sku = isset($variation['sku']) ? trim($variation['sku']) : null;

                    if (empty($sku)) {

                        $base_sku = generate_random_sku($name_pt, false);
                        $sku = generate_unique_sku($name_pt, false, $base_sku, $product_id);
                    }

                    $stock = filter_var($variation['stock'] ?? 0, FILTER_VALIDATE_INT); if ($stock === false || $stock < 0) $stock = 0;
                    $weight = isset($variation['weight']) && $variation['weight'] !== '' ? filter_var($variation['weight'], FILTER_VALIDATE_FLOAT) : null; if ($weight === false) $weight = null;

                    $price_override_raw = $variation['price_modifier_override'] ?? '';

                    if (trim($price_override_raw) === '') {
                        $price_override = null;
                    } else {
                        $price_override = filter_var($price_override_raw, FILTER_VALIDATE_FLOAT);
                        if ($price_override === false) {
                            $price_override = null;
                        }
                    }

                    $var_is_active = isset($variation['is_active']) && $variation['is_active'] == '1' ? 1 : 0;

                    $value_ids = [];
                    if (isset($variation['attributes']) && is_array($variation['attributes'])) {
                        $value_ids = array_filter(array_values($variation['attributes']), function($id) {
                            return filter_var($id, FILTER_VALIDATE_INT) && (int)$id > 0;
                        });
                    }

                    if (empty($value_ids)) {
                        continue;
                    }
                    sort($value_ids);

                    if ($is_new) {

                        if ($price_override === null) {
                            $var_sql = "INSERT INTO product_variations (product_id, sku, stock, weight, price_modifier_override, is_active) VALUES (:pid, :sku, :stock, :weight, NULL, :active)";
                            $var_params = [
                                ':pid' => $product_id,
                                ':sku' => $sku ?: null,
                                ':stock' => $stock,
                                ':weight' => $weight,
                                ':active' => $var_is_active
                            ];
                        } else {
                            $var_sql = "INSERT INTO product_variations (product_id, sku, stock, weight, price_modifier_override, is_active) VALUES (:pid, :sku, :stock, :weight, :price_override, :active)";
                            $var_params = [
                                ':pid' => $product_id,
                                ':sku' => $sku ?: null,
                                ':stock' => $stock,
                                ':weight' => $weight,
                                ':price_override' => $price_override,
                                ':active' => $var_is_active
                            ];
                        }
                        $stmt = db_query($var_sql, $var_params);
                        if (!$stmt) {
                            throw new Exception("Falha ao inserir variação.");
                        }
                        $variation_id = $pdo->lastInsertId();
                    } else {

                        if ($price_override === null) {
                            $var_sql = "UPDATE product_variations SET sku = :sku, stock = :stock, weight = :weight, price_modifier_override = NULL, is_active = :active WHERE id = :vid AND product_id = :pid";
                            $var_params = [
                                ':sku' => $sku ?: null,
                                ':stock' => $stock,
                                ':weight' => $weight,
                                ':active' => $var_is_active,
                                ':vid' => $variation_id,
                                ':pid' => $product_id
                            ];
                        } else {
                            $var_sql = "UPDATE product_variations SET sku = :sku, stock = :stock, weight = :weight, price_modifier_override = :price_override, is_active = :active WHERE id = :vid AND product_id = :pid";
                            $var_params = [
                                ':sku' => $sku ?: null,
                                ':stock' => $stock,
                                ':weight' => $weight,
                                ':price_override' => $price_override,
                                ':active' => $var_is_active,
                                ':vid' => $variation_id,
                                ':pid' => $product_id
                            ];
                        }
                        $updated_rows = db_query($var_sql, $var_params);
                        if ($updated_rows === false) {
                            throw new Exception("Falha ao atualizar variação ID $variation_id.");
                        }

                        db_query("DELETE FROM variation_values WHERE variation_id = :vid", [':vid' => $variation_id]);
                    }

                    foreach ($value_ids as $value_id) {
                        $link_params = [':vid' => $variation_id, ':value_id' => (int)$value_id];
                        db_query("INSERT INTO variation_values (variation_id, value_id) VALUES (:vid, :value_id)", $link_params);
                    }
                }
            }

            $images_to_delete = $_POST['delete_images'] ?? [];
            $deleted_image_count = 0;
            if (!empty($images_to_delete) && $action === 'edit') {
                $upload_dir_for_delete = __DIR__ . '/public/assets/images/products/';
                foreach ($images_to_delete as $image_id_to_delete) {
                    $image_id_to_delete = filter_var($image_id_to_delete, FILTER_VALIDATE_INT);
                    if ($image_id_to_delete) {

                        $image_data = db_query("SELECT filename FROM product_images WHERE id = :id AND product_id = :pid", [':id' => $image_id_to_delete, ':pid' => $product_id], true);
                        if ($image_data) {
                            $filename_to_delete = $image_data['filename'];
                            $file_path_to_delete = $upload_dir_for_delete . $filename_to_delete;

                            $deleted_db = db_query("DELETE FROM product_images WHERE id = :id", [':id' => $image_id_to_delete]);
                            if ($deleted_db) {

                                if (file_exists($file_path_to_delete)) {
                                    if (@unlink($file_path_to_delete)) {
                                        $deleted_image_count++;
                                    } else {
                                        $unlink_error = error_get_last();
                                        add_flash_message("Erro ao remover o ficheiro da imagem '$filename_to_delete' do servidor (verifique permissões). A referência foi removida da base de dados.", 'warning');
                                    }
                                } else {

                                }
                            } else {
                                 add_flash_message("Erro ao remover a referência da imagem ID $image_id_to_delete da base de dados.", 'danger');
                            }
                        } else {
                             add_flash_message("Imagem ID $image_id_to_delete não encontrada para este produto.", 'warning');
                        }
                    }
                }
                 if ($deleted_image_count > 0) {
                     add_flash_message("$deleted_image_count imagens removidas.", 'info');
                 }
            }

            $upload_dir = __DIR__ . '/public/assets/images/products/';
            $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            $max_file_size = 5 * 1024 * 1024;
            $watermark_path = __DIR__ . '/public/assets/images/watermark/watermark.png';
            $uploaded_image_filenames = [];

            if (!file_exists($upload_dir)) {
                mkdir($upload_dir, 0777, true);
            }

            if (isset($_FILES['product_images']) && is_array($_FILES['product_images']['name'])) {
                $image_count = count($_FILES['product_images']['name']);
                for ($i = 0; $i < $image_count; $i++) {
                    if ($_FILES['product_images']['error'][$i] === UPLOAD_ERR_OK) {
                        $tmp_name = $_FILES['product_images']['tmp_name'][$i];
                        $original_name = $_FILES['product_images']['name'][$i];
                        $file_size = $_FILES['product_images']['size'][$i];
                        $file_type = $_FILES['product_images']['type'][$i];

                        if (!in_array($file_type, $allowed_types)) {
                            add_flash_message("Tipo de ficheiro inválido para '$original_name'. Permitidos: JPG, PNG, GIF, WEBP.", 'warning');
                            continue;
                        }
                        if ($file_size > $max_file_size) {
                            add_flash_message("Ficheiro '$original_name' demasiado grande (Máx: " . ($max_file_size / 1024 / 1024) . " MB).", 'warning');
                            continue;
                        }

                        $file_extension = pathinfo($original_name, PATHINFO_EXTENSION);
                        $safe_original_name = preg_replace('/[^a-zA-Z0-9_\-\.]/', '', pathinfo($original_name, PATHINFO_FILENAME));
                        $unique_filename = 'prod_' . $product_id . '_' . time() . '_' . $i . '_' . $safe_original_name . '.' . $file_extension;
                        $destination = $upload_dir . $unique_filename;

                        if (move_uploaded_file($tmp_name, $destination)) {

                            $watermark_enabled = (bool) get_setting('watermark_enabled', false);
                            if ($watermark_enabled && function_exists('apply_watermark') && file_exists($watermark_path)) {
                                $watermark_success = apply_watermark($destination, $watermark_path);
                                if ($watermark_success) {
                                } else {
                                    add_flash_message("Aviso: Falha ao aplicar marca d'água a '$unique_filename'.", 'warning');

                                }
                            } elseif ($watermark_enabled && !file_exists($watermark_path)) {
                                 add_flash_message("Aviso: Ficheiro de marca d'água não encontrado, mas a funcionalidade está ativa nas configurações.", 'warning');
                            } elseif ($watermark_enabled && !function_exists('apply_watermark')) {
                                 add_flash_message("Aviso: Função de marca d'água não encontrada (erro de código).", 'warning');
                            }

                            $img_sql = "INSERT INTO product_images (product_id, filename, sort_order) VALUES (:pid, :fname, (SELECT IFNULL(MAX(sort_order), -1) + 1 FROM product_images WHERE product_id = :pid2))";
                            $img_params = [':pid' => $product_id, ':fname' => $unique_filename, ':pid2' => $product_id];
                            $inserted_img = db_query($img_sql, $img_params);
                            if ($inserted_img) {
                                $uploaded_image_filenames[] = $unique_filename;
                            } else {
                                add_flash_message("Erro ao guardar referência da imagem '$unique_filename' na base de dados.", 'danger');

                            }
                        } else {
                            add_flash_message("Erro ao mover o ficheiro '$original_name'.", 'danger');
                        }
                    } elseif ($_FILES['product_images']['error'][$i] !== UPLOAD_ERR_NO_FILE) {

                        add_flash_message("Erro no upload do ficheiro: " . upload_error_message($_FILES['product_images']['error'][$i]), 'danger');
                    }
                }
            }

            require_once __DIR__ . '/includes/product_info_fields.php';
            $info_fields = $_POST['info_fields'] ?? [];
            $info_fields_updated = 0;
            $info_fields_deleted = 0;
            $info_fields_added = 0;

            $existing_fields = db_query(
                "SELECT id, icon, text FROM product_info_fields WHERE product_id = :pid",
                [':pid' => $product_id],
                false, true
            );

            $existing_field_lookup = [];
            if ($existing_fields) {
                foreach ($existing_fields as $field) {
                    $key = $field['icon'] . '|' . $field['text'];
                    $existing_field_lookup[$key] = $field['id'];
                }
            }

            $processed_field_ids = [];

            foreach ($info_fields as $field_id => $field_data) {
                $is_new = strpos($field_id, 'new_') === 0;
                $delete_field = isset($field_data['delete']) && $field_data['delete'] == '1';
                $already_saved = isset($field_data['already_saved']) && $field_data['already_saved'] == '1';
                $icon = trim($field_data['icon'] ?? '');
                $text = trim($field_data['text'] ?? '');

                if (empty($icon) && empty($text) && !$delete_field) {
                    continue;
                }

                $field_key = $icon . '|' . $text;
                if (!$delete_field && isset($existing_field_lookup[$field_key])) {
                    $existing_id = $existing_field_lookup[$field_key];

                    if ($is_new) {
                        $processed_field_ids[] = $existing_id;
                        continue;
                    }

                    if (!$is_new && $field_id != $existing_id && !in_array($existing_id, $processed_field_ids)) {

                        if (update_product_info_field($existing_id, $icon, $text)) {
                            $info_fields_updated++;
                            $processed_field_ids[] = $existing_id;
                        }
                        continue;
                    }
                }

                if ($is_new && !$delete_field) {

                    $similar_exists = false;
                    foreach ($existing_field_lookup as $key => $id) {
                        list($existing_icon, $existing_text) = explode('|', $key);
                        if ($existing_icon === $icon && $existing_text === $text) {
                            $similar_exists = true;
                            $processed_field_ids[] = $id;
                            break;
                        }
                    }

                    if (!$similar_exists) {

                        $new_id = add_product_info_field($product_id, $icon, $text);
                        if ($new_id) {
                            $info_fields_added++;
                            $processed_field_ids[] = $new_id;
                        }
                    }
                }

                elseif (!$is_new && $delete_field) {

                    if (delete_product_info_field($field_id)) {
                        $info_fields_deleted++;
                    }
                }

                elseif (!$is_new && !$delete_field) {

                    if (update_product_info_field($field_id, $icon, $text)) {
                        $info_fields_updated++;
                        $processed_field_ids[] = $field_id;
                    }
                }
            }

            $videos_to_delete = $_POST['delete_videos'] ?? [];
            $deleted_video_count = 0;
            if (!empty($videos_to_delete) && $action === 'edit') {
                require_once __DIR__ . '/includes/product_video_functions.php';
                $upload_dir_for_delete_videos = __DIR__ . '/public/assets/videos/products/';
                $thumbnails_dir = __DIR__ . '/public/assets/images/video_thumbnails/';

                foreach ($videos_to_delete as $video_id_to_delete) {
                    $video_id_to_delete = filter_var($video_id_to_delete, FILTER_VALIDATE_INT);
                    if ($video_id_to_delete) {

                        $video_data = get_product_video_by_id($video_id_to_delete);
                        if ($video_data && $video_data['product_id'] == $product_id) {

                            if ($video_data['video_type'] === 'uploaded' && !empty($video_data['filename'])) {
                                $video_path = $upload_dir_for_delete_videos . $video_data['filename'];
                                if (file_exists($video_path)) {
                                    @unlink($video_path);
                                }
                            }

                            if (!empty($video_data['thumbnail_filename'])) {
                                $thumbnail_path = $thumbnails_dir . $video_data['thumbnail_filename'];
                                if (file_exists($thumbnail_path)) {
                                    @unlink($thumbnail_path);
                                }
                            }

                            if (delete_product_video($video_id_to_delete)) {
                                $deleted_video_count++;
                            } else {
                                add_flash_message("Erro ao remover o vídeo ID $video_id_to_delete da base de dados.", 'danger');
                            }
                        } else {
                            add_flash_message("Vídeo ID $video_id_to_delete não encontrado para este produto.", 'warning');
                        }
                    }
                }

                if ($deleted_video_count > 0) {
                    add_flash_message("$deleted_video_count vídeos removidos.", 'info');
                }
            }

            if (isset($_FILES['product_video']) && $_FILES['product_video']['error'] === UPLOAD_ERR_OK) {
                require_once __DIR__ . '/includes/product_video_functions.php';

                $upload_dir_videos = __DIR__ . '/public/assets/videos/products/';
                $thumbnails_dir = __DIR__ . '/public/assets/images/video_thumbnails/';

                if (!file_exists($upload_dir_videos)) {
                    mkdir($upload_dir_videos, 0777, true);
                }
                if (!file_exists($thumbnails_dir)) {
                    mkdir($thumbnails_dir, 0777, true);
                }

                $allowed_video_types = ['video/mp4', 'video/webm', 'video/ogg'];
                $max_video_size = 50 * 1024 * 1024;

                $tmp_name = $_FILES['product_video']['tmp_name'];
                $original_name = $_FILES['product_video']['name'];
                $file_size = $_FILES['product_video']['size'];
                $file_type = $_FILES['product_video']['type'];

                if (!in_array($file_type, $allowed_video_types)) {
                    add_flash_message("Tipo de ficheiro inválido para vídeo '$original_name'. Permitidos: MP4, WEBM, OGG.", 'warning');
                } else if ($file_size > $max_video_size) {
                    add_flash_message("Ficheiro de vídeo '$original_name' demasiado grande (Máx: " . ($max_video_size / 1024 / 1024) . " MB).", 'warning');
                } else {

                    $file_extension = pathinfo($original_name, PATHINFO_EXTENSION);
                    $safe_original_name = preg_replace('/[^a-zA-Z0-9_\-\.]/', '', pathinfo($original_name, PATHINFO_FILENAME));
                    $unique_filename = 'video_' . $product_id . '_' . time() . '_' . $safe_original_name . '.' . $file_extension;
                    $destination = $upload_dir_videos . $unique_filename;

                    if (move_uploaded_file($tmp_name, $destination)) {

                        $thumbnail_filename = null;
                        $thumbnail_path = $thumbnails_dir . 'thumb_' . $unique_filename . '.jpg';
                        if (generate_video_thumbnail($destination, $thumbnail_path)) {
                            $thumbnail_filename = 'thumb_' . $unique_filename . '.jpg';
                        } else {
                        }

                        $video_id = add_product_video(
                            $product_id,
                            'uploaded',
                            $unique_filename,
                            null,
                            $thumbnail_filename
                        );

                        if ($video_id) {
                            add_flash_message("Vídeo '$safe_original_name' carregado com sucesso.", 'success');
                        } else {
                            add_flash_message("Erro ao guardar referência do vídeo '$unique_filename' na base de dados.", 'danger');

                            @unlink($destination);
                            if ($thumbnail_filename) {
                                @unlink($thumbnail_path);
                            }
                        }
                    } else {
                        add_flash_message("Erro ao mover o ficheiro de vídeo '$original_name'.", 'danger');
                    }
                }
            }

            $video_url = trim($_POST['video_url'] ?? '');
            if (!empty($video_url)) {
                require_once __DIR__ . '/includes/product_video_functions.php';

                if (filter_var($video_url, FILTER_VALIDATE_URL)) {

                    $youtube_id = extract_youtube_video_id($video_url);
                    if ($youtube_id) {

                        $video_id = add_product_video(
                            $product_id,
                            'external',
                            null,
                            $video_url,
                            null
                        );

                        if ($video_id) {
                            add_flash_message("URL de vídeo externo adicionado com sucesso.", 'success');
                        } else {
                            add_flash_message("Erro ao guardar URL de vídeo externo na base de dados.", 'danger');
                        }
                    } else {
                        add_flash_message("URL de vídeo inválido. Apenas URLs do YouTube são suportados atualmente.", 'warning');
                    }
                } else {
                    add_flash_message("URL inválido. Por favor, insira um URL válido.", 'warning');
                }
            }

            require_once __DIR__ . '/includes/custom_field_functions.php';
            $custom_fields = $_POST['custom_fields'] ?? [];
            $custom_fields_updated = false;

            if (!empty($custom_fields)) {

                if (save_product_custom_fields_ajax($product_id, $custom_fields)) {
                    $custom_fields_updated = true;
                } else {
                    add_flash_message("Aviso: Falha ao guardar campos personalizados.", 'warning');
                }
            } else {
            }
            $pdo->commit();

            $success_message = 'Produto guardado com sucesso!';
            if (count($uploaded_image_filenames) > 0) {
                $success_message .= ' ' . count($uploaded_image_filenames) . ' imagens carregadas.';
            }
            if ($info_fields_added > 0 || $info_fields_updated > 0 || $info_fields_deleted > 0) {
                $info_fields_message = [];
                if ($info_fields_added > 0) $info_fields_message[] = $info_fields_added . ' campos adicionados';
                if ($info_fields_updated > 0) $info_fields_message[] = $info_fields_updated . ' campos atualizados';
                if ($info_fields_deleted > 0) $info_fields_message[] = $info_fields_deleted . ' campos removidos';
                $success_message .= ' Informações adicionais: ' . implode(', ', $info_fields_message) . '.';
            }

            if ($custom_fields_updated) {
                $success_message .= ' Campos personalizados atualizados.';
            }

            add_flash_message($success_message, 'success');

        } catch (Exception $e) {
            if ($pdo->inTransaction()) {
                 $pdo->rollBack();
            } else {
            }
            add_flash_message('Erro ao guardar o produto: ' . $e->getMessage(), 'danger');
            $_SESSION['form_data'] = $_POST;
            $redirect_url = 'admin.php?section=products&action=' . $action;
            if ($item_id) $redirect_url .= '&id=' . $item_id;
            header('Location: ' . $redirect_url . '&' . get_session_id_param());
            exit;
        }

        $default_image_id_post = filter_input(INPUT_POST, 'default_image_id', FILTER_VALIDATE_INT);
        if ($product_id && function_exists('set_default_product_image')) {
             if (!set_default_product_image($product_id, $default_image_id_post)) {

                 add_flash_message("Aviso: Falha ao definir a imagem padrão.", 'warning');
             }
        }

        $save_action = $_POST['save_action'] ?? 'save_and_return';

        if ($save_action === 'save_and_continue') {

            header('Location: admin.php?section=products&action=edit&id=' . $product_id . '&' . get_session_id_param());
        } else {

            header('Location: admin.php?section=products&' . get_session_id_param());
        }
        exit;
    }

    elseif ($section === 'attributes' && ($action === 'new' || ($action === 'edit' && $item_id))) {
        $name_pt = trim($_POST['name_pt'] ?? '');
        $errors = [];
        if (empty($name_pt)) $errors[] = "Nome do atributo é obrigatório.";
        $name_check_sql = "SELECT id FROM attributes WHERE LOWER(name_pt) = LOWER(:name)";
        $name_check_params = [':name' => $name_pt];
        if ($action === 'edit') {
            $name_check_sql .= " AND id != :id";
            $name_check_params[':id'] = $item_id;
        }
        $existing_name = db_query($name_check_sql, $name_check_params, true);
        if ($existing_name) $errors[] = "O nome de atributo '$name_pt' já está em uso.";

        if (!empty($errors)) {
            foreach ($errors as $error) add_flash_message($error, 'danger');
            $_SESSION['form_data'] = $_POST;
            $redirect_url = 'admin.php?section=attributes&action=' . $action;
            if ($item_id) $redirect_url .= '&id=' . $item_id;
            header('Location: ' . $redirect_url . '&' . get_session_id_param());
            exit;
        }
        try {
            if ($action === 'new') {
                $sql = "INSERT INTO attributes (name_pt) VALUES (:name_pt)";
                $params = [':name_pt' => $name_pt];
                $new_id = db_query($sql, $params);
                if ($new_id) add_flash_message('Atributo adicionado com sucesso!', 'success');
                else throw new Exception("Falha ao inserir atributo.");
            } else {
                $sql = "UPDATE attributes SET name_pt = :name_pt WHERE id = :id";
                $params = [':name_pt' => $name_pt, ':id' => $item_id];
                $updated = db_query($sql, $params);
                if ($updated !== false) add_flash_message('Atributo atualizado com sucesso!', 'success');
                else throw new Exception("Falha ao atualizar atributo.");
            }
        } catch (Exception $e) {
             add_flash_message('Erro ao guardar o atributo: ' . $e->getMessage(), 'danger');
             $_SESSION['form_data'] = $_POST;
             $redirect_url = 'admin.php?section=attributes&action=' . $action;
             if ($item_id) $redirect_url .= '&id=' . $item_id;
             header('Location: ' . $redirect_url . '&' . get_session_id_param());
             exit;
        }
        header('Location: admin.php?section=attributes&' . get_session_id_param());
        exit;
    }

    elseif ($section === 'attributes' && $action === 'values' && isset($_POST['action_type']) && isset($_POST['attribute_id'])) {
        
        $attribute_id = filter_input(INPUT_POST, 'attribute_id', FILTER_VALIDATE_INT);
        $action_type = $_POST['action_type'] ?? '';
        $value_id = isset($_POST['value_id']) ? filter_input(INPUT_POST, 'value_id', FILTER_VALIDATE_INT) : null;
        
        if (!$attribute_id) {
            add_flash_message('ID do Atributo inválido.', 'danger');
        } else {
            $value_pt = trim($_POST['value_pt'] ?? '');
            $price_modifier = filter_var($_POST['price_modifier'] ?? 0, FILTER_VALIDATE_FLOAT);
            
            $errors = [];
            
            
            if (empty($value_pt)) {
                $errors[] = "O valor não pode estar vazio.";
            } elseif (strlen($value_pt) > 255) {
                $errors[] = "O valor não pode ter mais de 255 caracteres.";
            }
            
            if ($price_modifier === false) $price_modifier = 0.0;
            
            
            if (empty($errors)) {
                $check_sql = "SELECT id FROM attribute_values WHERE attribute_id = :aid AND value_pt = :val";
                $check_params = [':aid' => $attribute_id, ':val' => $value_pt];
                
                if ($action_type === 'edit' && $value_id) {
                    $check_sql .= " AND id != :id";
                    $check_params[':id'] = $value_id;
                }
                
                $existing = db_query($check_sql, $check_params, true);
                if ($existing) {
                    $errors[] = "O valor '$value_pt' já existe para este atributo.";
                }
            }
            
            if (!empty($errors)) {
                foreach ($errors as $error) {
                    add_flash_message($error, 'danger');
                }
                $_SESSION['form_data'] = $_POST;
                $redirect_url = 'admin.php?section=attributes&action=values&attribute_id=' . $attribute_id . '&action_type=' . $action_type;
                if ($value_id) $redirect_url .= '&value_id=' . $value_id;
                $redirect_url .= '&' . get_session_id_param();
                header('Location: ' . $redirect_url);
                exit;
            }
            
            try {
                if ($action_type === 'add') {
                    
                    $sql = "INSERT INTO attribute_values (attribute_id, value_pt, price_modifier) VALUES (:aid, :val, :pmod)";
                    $params = [':aid' => $attribute_id, ':val' => $value_pt, ':pmod' => $price_modifier];
                    $result = db_query($sql, $params);
                    
                    if ($result) {
                        add_flash_message('Valor do atributo criado com sucesso.', 'success');
                    } else {
                        add_flash_message('Erro ao criar valor do atributo.', 'danger');
                    }
                } elseif ($action_type === 'edit' && $value_id) {
                    
                    $sql = "UPDATE attribute_values SET value_pt = :val, price_modifier = :pmod WHERE id = :id AND attribute_id = :aid";
                    $params = [':val' => $value_pt, ':pmod' => $price_modifier, ':id' => $value_id, ':aid' => $attribute_id];
                    $result = db_query($sql, $params);
                    
                    if ($result !== false) {
                        add_flash_message('Valor do atributo atualizado com sucesso.', 'success');
                    } else {
                        add_flash_message('Erro ao atualizar valor do atributo.', 'danger');
                    }
                }
            } catch (Exception $e) {
                add_flash_message('Erro ao guardar valor: ' . $e->getMessage(), 'danger');
            }
        }
        
        header('Location: admin.php?section=attributes&action=values&attribute_id=' . $attribute_id . '&' . get_session_id_param());
        exit;
    }
    elseif ($section === 'attributes' && $action === 'values' && isset($_POST['attribute_id'])) {
        $attribute_id = filter_input(INPUT_POST, 'attribute_id', FILTER_VALIDATE_INT);
        if (!$attribute_id) {
             add_flash_message('ID do Atributo inválido.', 'danger');
        } else {
            $values_data = $_POST['values'] ?? [];
            $pdo = get_db_connection();
            $errors = [];
            $success_count = 0;

            try {
                $pdo->beginTransaction();

                foreach ($values_data as $value_id => $data) {
                    $value_pt = trim($data['value_pt'] ?? '');
                    $price_modifier = filter_var($data['price_modifier'] ?? 0, FILTER_VALIDATE_FLOAT);
                    $delete_value = isset($data['delete']) && $data['delete'] == '1';

                    $is_new = (strpos($value_id, 'new_') === 0);

                    if ($delete_value && !$is_new) {

                        $value_id_int = filter_var($value_id, FILTER_VALIDATE_INT);
                        if ($value_id_int) {
                             db_query("DELETE FROM attribute_values WHERE id = :id AND attribute_id = :aid", [':id' => $value_id_int, ':aid' => $attribute_id]);
                             $success_count++;
                        } else {
                             $errors[] = "ID inválido para remoção: " . sanitize_input($value_id);
                        }
                        continue;
                    }

                    if (empty($value_pt)) {

                        if (!$is_new) {
                             $errors[] = "O nome do valor não pode estar vazio (ID: $value_id).";
                        }
                        continue;
                    }
                    if ($price_modifier === false) $price_modifier = 0.0;

                    if (!$delete_value) {
                        $check_sql = "SELECT id FROM attribute_values WHERE attribute_id = :aid AND value_pt = :val";
                        $check_params = [':aid' => $attribute_id, ':val' => $value_pt];
                        if (!$is_new) {

                            $value_id_int = filter_var($value_id, FILTER_VALIDATE_INT);
                            if ($value_id_int) {
                                $check_sql .= " AND id != :id";
                                $check_params[':id'] = $value_id_int;
                            } else {

                            }
                        }
                        $existing = db_query($check_sql, $check_params, true);
                        if ($existing) {
                            $errors[] = "O valor '$value_pt' já existe para este atributo.";
                            continue;
                        }
                    }

                    if ($is_new) {

                        if ($delete_value) continue;

                        $sql = "INSERT INTO attribute_values (attribute_id, value_pt, price_modifier) VALUES (:aid, :val, :pmod)";
                        $params = [':aid' => $attribute_id, ':val' => $value_pt, ':pmod' => $price_modifier];
                        $stmt = db_query($sql, $params);
                        if ($stmt) {
                            $new_id = $pdo->lastInsertId();
                            $success_count++;
                        } else {
                            $errors[] = "Falha ao inserir valor '$value_pt'.";
                        }
                    } else {
                        if ($delete_value) continue;

                        $value_id_int = filter_var($value_id, FILTER_VALIDATE_INT);
                        if (!$value_id_int) {
                             $errors[] = "ID de valor existente inválido para atualização: " . sanitize_input($value_id);
                             continue;
                        }
                        $sql = "UPDATE attribute_values SET value_pt = :val, price_modifier = :pmod WHERE id = :id AND attribute_id = :aid";
                        $params = [':val' => $value_pt, ':pmod' => $price_modifier, ':id' => $value_id_int, ':aid' => $attribute_id];
                        $updated = db_query($sql, $params);
                        if ($updated !== false) {
                             $success_count++;
                        } else {
                             $errors[] = "Falha ao atualizar valor '$value_pt'.";
                        }
                    }
                }

                if (!empty($errors)) throw new Exception(implode("<br>", $errors));

                $pdo->commit();
                if ($success_count > 0) add_flash_message('Valores do atributo guardados com sucesso.', 'success');

            } catch (Exception $e) {
                if ($pdo->inTransaction()) {
                    $pdo->rollBack();
                }
                add_flash_message('Erro ao guardar valores: ' . $e->getMessage(), 'danger');
            }
        }

        header('Location: admin.php?section=attributes&action=values&attribute_id=' . $attribute_id . '&' . get_session_id_param());
        exit;
    }

    elseif ($section === 'page_categories' && ($action === 'new' || ($action === 'edit' && $item_id))) {
        $name = trim($_POST['name'] ?? '');
        $slug = trim($_POST['slug'] ?? '');

        $show_in_header = isset($_POST['show_in_header']) && $_POST['show_in_header'] == '1';
        $errors = [];

        if (empty($name)) $errors[] = "Nome da categoria é obrigatório.";

        if (!empty($slug)) {
            $slug = generate_slug($slug);
            $slug_check_sql = "SELECT id FROM page_categories WHERE slug = :slug";
            $slug_check_params = [':slug' => $slug];
            if ($action === 'edit') {
                $slug_check_sql .= " AND id != :id";
                $slug_check_params[':id'] = $item_id;
            }
            $existing_slug = db_query($slug_check_sql, $slug_check_params, true);
            if ($existing_slug) $errors[] = "O slug '$slug' já está em uso. Escolha outro ou deixe em branco.";
        }

        $name_check_sql = "SELECT id FROM page_categories WHERE LOWER(name) = LOWER(:name)";
        $name_check_params = [':name' => $name];
        if ($action === 'edit') {
            $name_check_sql .= " AND id != :id";
            $name_check_params[':id'] = $item_id;
        }
        $existing_name = db_query($name_check_sql, $name_check_params, true);
        if ($existing_name) $errors[] = "O nome de categoria '$name' já está em uso.";

        if (!empty($errors)) {
            foreach ($errors as $error) add_flash_message($error, 'danger');
            $_SESSION['form_data'] = $_POST;
            $redirect_url = 'admin.php?section=page_categories&action=' . $action;
            if ($item_id) $redirect_url .= '&id=' . $item_id;
            header('Location: ' . $redirect_url . '&' . get_session_id_param());
            exit;
        }

        try {
            if ($action === 'new') {

                $new_id = create_page_category($name, $slug, $show_in_header);
                if ($new_id) {
                    add_flash_message('Categoria de página criada com sucesso!', 'success');
                } else {
                    throw new Exception("Falha ao criar categoria de página.");
                }
            } else {

                $updated = update_page_category($item_id, $name, $slug, $show_in_header);
                if ($updated) {
                    add_flash_message('Categoria de página atualizada com sucesso!', 'success');
                } else {

                     if (!get_page_category_by_id($item_id)) {
                         throw new Exception("Categoria de página não encontrada para atualização (ID: $item_id).");
                     } else {
                         throw new Exception("Falha ao atualizar categoria de página (verifique se houve alterações ou erro na base de dados).");
                     }
                }
            }
            header('Location: admin.php?section=page_categories&' . get_session_id_param());
            exit;
        } catch (Exception $e) {
            add_flash_message('Erro ao guardar categoria de página: ' . $e->getMessage(), 'danger');
            $_SESSION['form_data'] = $_POST;
            $redirect_url = 'admin.php?section=page_categories&action=' . $action;
            if ($item_id) $redirect_url .= '&id=' . $item_id;
            header('Location: ' . $redirect_url . '&' . get_session_id_param());
            exit;
        }
    }

    elseif ($section === 'page_placeholders' && ($action === 'new' || ($action === 'edit' && $item_id))) {

        $name = trim($_POST['name'] ?? '');
        $slug = trim($_POST['slug'] ?? '');

        $errors = [];
        if (empty($name)) $errors[] = "Nome do placeholder é obrigatório.";

        if (empty($slug)) {
            $slug = generate_slug($name);
        } else {
            $slug = generate_slug($slug);
        }

        $slug_check_sql = "SELECT id FROM page_placeholders WHERE slug = :slug";
        $slug_check_params = [':slug' => $slug];
        if ($action === 'edit') {
            $slug_check_sql .= " AND id != :id";
            $slug_check_params[':id'] = $item_id;
        }
        $existing_slug = db_query($slug_check_sql, $slug_check_params, true);
        if ($existing_slug) $errors[] = "O slug '$slug' já está em uso. Escolha outro.";

        if (!empty($errors)) {
            foreach ($errors as $error) add_flash_message($error, 'danger');
            $_SESSION['form_data'] = $_POST;
            $redirect_url = 'admin.php?section=page_placeholders&action=' . $action;
            if ($item_id) $redirect_url .= '&id=' . $item_id;
            header('Location: ' . $redirect_url . '&' . get_session_id_param());
            exit;
        }

        try {
            if ($action === 'new') {
                $new_id = create_page_placeholder($name, $slug);
                if ($new_id) {
                    add_flash_message('Placeholder criado com sucesso!', 'success');
                } else {
                    throw new Exception("Falha ao criar placeholder.");
                }
            } else {
                $updated = update_page_placeholder($item_id, $name, $slug);
                if ($updated) {
                    add_flash_message('Placeholder atualizado com sucesso!', 'success');
                } else {

                     if (!get_page_placeholder_by_id($item_id)) {
                         throw new Exception("Placeholder não encontrado para atualização (ID: $item_id).");
                     } else {
                         throw new Exception("Falha ao atualizar placeholder (verifique se houve alterações ou erro na base de dados).");
                     }
                }
            }
            header('Location: admin.php?section=page_placeholders&' . get_session_id_param());
            exit;
        } catch (Exception $e) {
            add_flash_message('Erro ao guardar placeholder: ' . $e->getMessage(), 'danger');
            $_SESSION['form_data'] = $_POST;
            $redirect_url = 'admin.php?section=page_placeholders&action=' . $action;
            if ($item_id) $redirect_url .= '&id=' . $item_id;
            header('Location: ' . $redirect_url . '&' . get_session_id_param());
            exit;
        }
    }

    elseif ($section === 'pages' && ($action === 'new' || ($action === 'edit' && $item_id))) {

        $title = trim($_POST['title_pt'] ?? '');
        $slug = trim($_POST['slug'] ?? '');
        $content = trim($_POST['content_pt'] ?? '');
        $is_active = isset($_POST['is_active']) && $_POST['is_active'] == '1';
        $category_id = filter_input(INPUT_POST, 'category_id', FILTER_VALIDATE_INT);
        $placeholder_id = filter_input(INPUT_POST, 'placeholder_id', FILTER_VALIDATE_INT);
        $show_in_header = isset($_POST['show_in_header']) && $_POST['show_in_header'] == '1';

        $show_in_footer = false;
        $require_agreement = isset($_POST['require_agreement_checkout']) && $_POST['require_agreement_checkout'] == '1';
        $require_agreement_digital = isset($_POST['require_agreement_digital_checkout']) && $_POST['require_agreement_digital_checkout'] == '1';
        $show_title = isset($_POST['show_title']) && $_POST['show_title'] == '1';

        $seo_title_post = trim($_POST['seo_title'] ?? '');
        $seo_description_post = trim($_POST['seo_description'] ?? '');
        $seo_keywords_post = trim($_POST['seo_keywords'] ?? '');
        $og_title_post = trim($_POST['og_title'] ?? '');
        $og_description_post = trim($_POST['og_description'] ?? '');
        $og_image_post = trim($_POST['og_image'] ?? '');
        $twitter_card_post = trim($_POST['twitter_card'] ?? 'summary');
        $twitter_title_post = trim($_POST['twitter_title'] ?? '');
        $twitter_description_post = trim($_POST['twitter_description'] ?? '');
        $twitter_image_post = trim($_POST['twitter_image'] ?? '');

        $errors = [];
        if (empty($title)) $errors[] = "Título da página é obrigatório.";

        $is_system_page = false;
        if ($action === 'edit') {
            $original_page = get_page_by_id($item_id);
            $is_system_page = (bool)($original_page['is_system_page'] ?? 0);
        }

        if (!$is_system_page) {
            if (empty($slug)) {
                $slug = generate_slug($title);
            } else {
                $slug = generate_slug($slug);
            }

            $slug_check_sql = "SELECT id FROM pages WHERE slug = :slug";
            $slug_check_params = [':slug' => $slug];
            if ($action === 'edit') {
                $slug_check_sql .= " AND id != :id";
                $slug_check_params[':id'] = $item_id;
            }
            $existing_slug = db_query($slug_check_sql, $slug_check_params, true);
            if ($existing_slug) $errors[] = "O slug '$slug' já está em uso. Escolha outro.";
        } elseif ($action === 'new') {

             if (empty($slug)) $errors[] = "Slug é obrigatório para páginas de sistema (e não pode ser alterado).";
        }

        if (!empty($errors)) {
            foreach ($errors as $error) add_flash_message($error, 'danger');
            $_SESSION['form_data'] = $_POST;
            $redirect_url = 'admin.php?section=pages&action=' . $action;
            if ($item_id) $redirect_url .= '&id=' . $item_id;
            header('Location: ' . $redirect_url . '&' . get_session_id_param());
            exit;
        }

        try {
            $page_id_for_redirect = null;
            if ($action === 'new') {
                $new_page_id = create_page(
                    $title, $content, $is_active, $slug, $category_id,
                    $show_in_header, $show_in_footer, $require_agreement, $placeholder_id,
                    $require_agreement_digital, $show_title,

                    $seo_title_post, $seo_description_post, $seo_keywords_post,
                    $og_title_post, $og_description_post, $og_image_post,
                    $twitter_card_post, $twitter_title_post, $twitter_description_post, $twitter_image_post
                );
                if ($new_page_id) {
                    add_flash_message('Página criada com sucesso!', 'success');
                    $page_id_for_redirect = $new_page_id;
                } else {
                    throw new Exception("Falha ao criar página.");
                }
            } else {
                $updated = update_page(
                    $item_id, $title, $content, $is_active, $slug, $category_id,
                    $show_in_header, $show_in_footer, $require_agreement, $placeholder_id,
                    $require_agreement_digital, $show_title,

                    $seo_title_post, $seo_description_post, $seo_keywords_post,
                    $og_title_post, $og_description_post, $og_image_post,
                    $twitter_card_post, $twitter_title_post, $twitter_description_post, $twitter_image_post
                );
                if ($updated) {
                    add_flash_message('Página atualizada com sucesso!', 'success');
                    $page_id_for_redirect = $item_id;
                } else {

                     if (!get_page_by_id($item_id)) {
                         throw new Exception("Página não encontrada para atualização (ID: $item_id).");
                     } else {

                         $page_id_for_redirect = $item_id;

                     }
                }
            }

            $save_action_param = $_POST['save_action'] ?? 'save_and_return';

            if ($save_action_param === 'save_and_continue' && $page_id_for_redirect) {
                header('Location: admin.php?section=pages&action=edit&id=' . $page_id_for_redirect . '&' . get_session_id_param());
            } else {
                header('Location: admin.php?section=pages&' . get_session_id_param());
            }
            exit;
        } catch (Exception $e) {
            add_flash_message('Erro ao guardar página: ' . $e->getMessage(), 'danger');
            $_SESSION['form_data'] = $_POST;
            $redirect_url = 'admin.php?section=pages&action=' . $action;
            if ($item_id) $redirect_url .= '&id=' . $item_id;
            header('Location: ' . $redirect_url . '&' . get_session_id_param());
            exit;
        }
    }

    elseif ($section === 'categories' && ($action === 'new' || ($action === 'edit' && $item_id))) {
        $name = trim($_POST['category_name'] ?? '');
        $slug = generate_slug($name);
        $errors = [];

        if (empty($name)) {
            $errors[] = "Nome da categoria é obrigatório.";
        } else {

            $name_check_sql = "SELECT id FROM categories WHERE LOWER(name) = LOWER(:name)";
            $name_check_params = [':name' => $name];
            if ($action === 'edit') {
                $name_check_sql .= " AND id != :id";
                $name_check_params[':id'] = $item_id;
            }
            $existing_name = db_query($name_check_sql, $name_check_params, true);
            if ($existing_name) {
                $errors[] = "O nome de categoria '$name' já está em uso.";
            }

            $slug_check_sql = "SELECT id FROM categories WHERE slug = :slug";
            $slug_check_params = [':slug' => $slug];
             if ($action === 'edit') {
                $slug_check_sql .= " AND id != :id";
                $slug_check_params[':id'] = $item_id;
            }
            $existing_slug = db_query($slug_check_sql, $slug_check_params, true);
            if ($existing_slug) {

                $errors[] = "O slug '$slug' (derivado do nome) já está em uso. Altere o nome da categoria ligeiramente.";
            }
        }

        if (!empty($errors)) {
            foreach ($errors as $error) add_flash_message($error, 'danger');
            $_SESSION['form_data'] = $_POST;
            $redirect_url = 'admin.php?section=categories&action=' . $action;
            if ($item_id) $redirect_url .= '&id=' . $item_id;
            header('Location: ' . $redirect_url . '&' . get_session_id_param());
            exit;
        }

        try {
            $category_data = [
                'name' => $name,
                'slug' => $slug
            ];
            if ($action === 'new') {
                $new_id = addCategory($category_data);
                if ($new_id) {
                    add_flash_message('Categoria de produto adicionada com sucesso!', 'success');
                } else {
                    throw new Exception("Falha ao adicionar categoria de produto. O nome pode já existir.");
                }
            } else {
                $updated = updateCategory($item_id, $category_data);
                if ($updated) {
                    add_flash_message('Categoria de produto atualizada com sucesso!', 'success');
                } else {
                     if (!getCategoryById($item_id)) {
                         throw new Exception("Categoria de produto não encontrada para atualização (ID: $item_id).");
                     } else {
                         throw new Exception("Falha ao atualizar categoria de produto. O nome pode já existir.");
                     }
                }
            }
            header('Location: admin.php?section=categories&' . get_session_id_param());
            exit;
        } catch (Exception $e) {
            add_flash_message('Erro ao guardar categoria de produto: ' . $e->getMessage(), 'danger');
            $_SESSION['form_data'] = $_POST;
            $redirect_url = 'admin.php?section=categories&action=' . $action;
            if ($item_id) $redirect_url .= '&id=' . $item_id;
            header('Location: ' . $redirect_url . '&' . get_session_id_param());
            exit;
        }
    }

    elseif (isset($_POST['action']) && $_POST['action'] === 'reply_message') {
        header('Content-Type: application/json');
        $response = ['success' => false, 'message' => 'Erro desconhecido.'];

        $message_id = filter_input(INPUT_POST, 'message_id', FILTER_VALIDATE_INT);
        $reply_body = trim($_POST['reply_message'] ?? '');

        if (!$message_id || empty($reply_body)) {
            $response['message'] = 'ID da mensagem ou corpo da resposta em falta.';
            echo json_encode($response);
            exit;
        }

        $pdo = get_db_connection();
        if (!$pdo) {
             $response['message'] = 'Erro ao conectar à base de dados.';
             echo json_encode($response);
             exit;
        }

        try {
            $pdo->beginTransaction();

            $msg_sql = "SELECT email, name, subject FROM contacts WHERE id = :id";

            $original_message = db_query($msg_sql, [':id' => $message_id], true, false);

            if (!$original_message) {

                 if ($original_message === false) {
                     throw new Exception('Erro ao consultar a mensagem original.');
                 } else {
                     throw new Exception('Mensagem original não encontrada (ID: ' . $message_id . ').');
                 }
            }

            $recipient_email = $original_message['email'];
            $recipient_name = $original_message['name'];
            $original_subject = $original_message['subject'];

            $reply_sql = "INSERT INTO message_replies (message_id, reply_body) VALUES (:msg_id, :body)";
            $reply_params = [
                ':msg_id' => $message_id,
                ':body' => sanitize_input($reply_body)
            ];

            $inserted_reply_id = db_query($reply_sql, $reply_params, false, false);
            if ($inserted_reply_id === false) {

                throw new Exception('Falha ao guardar a resposta na base de dados. Verifique a estrutura da base de dados.');
            }

            $update_status_sql = "UPDATE contacts SET status = 'replied', replied_at = datetime('now', 'localtime') WHERE id = :id";

            $updated_status_count = db_query($update_status_sql, [':id' => $message_id], false, false);
            if ($updated_status_count === false) {
                 throw new Exception('Falha ao executar a atualização do estado da mensagem original.');
            } elseif ($updated_status_count === 0) {

            } else {
            }

            $email_subject = "Re: " . $original_subject;

            $store_name = get_setting('store_name', 'A Nossa Loja');

            $email_body_html = "<p>Olá " . sanitize_input($recipient_name) . ",</p>";
            $email_body_html .= "<p>  esta é uma mensagem em resposta ao seu contacto com o assunto '" . sanitize_input($original_subject) . "':</p><br>";
            $email_body_html .= "<blockquote style='border-left: 4px solid #ccc; padding-left: 15px; margin-left: 5px;'>";
            $email_body_html .= nl2br(sanitize_input($reply_body));
            $email_body_html .= "</blockquote><br>";
            $email_body_html .= "<p>Com os melhores cumprimentos,<br>" . $store_name . "</p>";

            $email_sent = send_email(
                $recipient_email,
                $recipient_name,
                $email_subject,
                $email_body_html
            );

            if (!$email_sent) {

                 $response['warning'] = 'Resposta guardada, mas ocorreu um erro ao enviar o email de notificação.';
            }

            $pdo->commit();

            $response['success'] = true;
            $response['message'] = 'Resposta guardada e enviada com sucesso!';
            if (isset($response['warning'])) {
                $response['message'] .= ' ' . $response['warning'];
            }

        } catch (Exception $e) {
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            $response['message'] = 'Erro: ' . $e->getMessage();
        }

        echo json_encode($response);
        exit;
    }

    elseif ($section === 'orders' && $action === 'detail' && isset($_POST['contact_client'])) {
        $order_id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT) ?: $item_id;

        if (!$order_id) {
            add_flash_message('ID da encomenda inválido.', 'danger');
            header('Location: admin.php?section=orders&' . get_session_id_param());
            exit;
        }

        $client_name = sanitize_input($_POST['client_name'] ?? '');
        $client_email = sanitize_input($_POST['client_email'] ?? '');
        $order_ref = sanitize_input($_POST['order_ref'] ?? '');
        $message_subject = sanitize_input($_POST['message_subject'] ?? '');
        $message_body = sanitize_input($_POST['message_body'] ?? '');

        if (empty($client_email) || empty($message_subject) || empty($message_body)) {
            add_flash_message('Todos os campos são obrigatórios.', 'danger');
            header('Location: admin.php?section=orders&action=detail&id=' . $order_id . '&' . get_session_id_param());
            exit;
        }

        $sql = "INSERT INTO contacts (
                    name,
                    email,
                    subject,
                    message,
                    status,
                    order_id,
                    is_from_admin,
                    created_at
                ) VALUES (
                    :name,
                    :email,
                    :subject,
                    :message,
                    'replied',
                    :order_id,
                    1,
                    datetime('now', 'localtime')
                )";

        $params = [
            ':name' => $client_name,
            ':email' => $client_email,
            ':subject' => $message_subject,
            ':message' => $message_body,
            ':order_id' => $order_id
        ];

        $result = db_query($sql, $params);

        if ($result) {

            $email_subject = "Ref: " . $order_ref . " - " . $message_subject;
            $email_body_html = "<p>Olá " . htmlspecialchars($client_name) . ",</p>";
            $email_body_html .= "<p>" . nl2br(htmlspecialchars($message_body)) . "</p>";
            $email_body_html .= "<p>Cumprimentos,<br>" . htmlspecialchars($settings['store_name'] ?? 'Loja Online') . "</p>";

            $email_sent = send_email(
                $client_email,
                $client_name,
                $email_subject,
                $email_body_html
            );

            if ($email_sent) {
                add_flash_message('Mensagem enviada com sucesso para o cliente.', 'success');
            } else {
                add_flash_message('Mensagem guardada, mas ocorreu um erro ao enviar o email.', 'warning');
            }
        } else {
            add_flash_message('Erro ao guardar a mensagem.', 'danger');
        }

        header('Location: admin.php?section=orders&action=detail&id=' . $order_id . '&' . get_session_id_param());
        exit;
    }

    elseif ($section === 'orders' && $action === 'detail' && isset($_POST['reply_to_message'])) {
        $order_id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT) ?: $item_id;
        $message_id = filter_input(INPUT_POST, 'message_id', FILTER_VALIDATE_INT);
        $reply_message = sanitize_input($_POST['reply_message'] ?? '');

        if (!$order_id || !$message_id || empty($reply_message)) {
            add_flash_message('Dados inválidos para resposta.', 'danger');
            header('Location: admin.php?section=orders&action=detail&id=' . $order_id . '&' . get_session_id_param());
            exit;
        }

        $original_message = db_query("SELECT * FROM contacts WHERE id = :id", [':id' => $message_id], true);

        if (!$original_message) {
            add_flash_message('Mensagem original não encontrada.', 'danger');
            header('Location: admin.php?section=orders&action=detail&id=' . $order_id . '&' . get_session_id_param());
            exit;
        }

        $sql = "INSERT INTO message_replies (
                    message_id,
                    reply_body,
                    sent_at
                ) VALUES (
                    :message_id,
                    :reply_body,
                    datetime('now', 'localtime')
                )";

        $params = [
            ':message_id' => $message_id,
            ':reply_body' => $reply_message
        ];

        $result = db_query($sql, $params);

        if ($result) {

            db_query(
                "UPDATE contacts SET status = 'replied', replied_at = datetime('now', 'localtime') WHERE id = :id",
                [':id' => $message_id]
            );

            $email_subject = "Re: " . $original_message['subject'];
            $email_body_html = "<p>Olá " . htmlspecialchars($original_message['name']) . ",</p>";
            $email_body_html .= "<p>" . nl2br(htmlspecialchars($reply_message)) . "</p>";
            $email_body_html .= "<p>Cumprimentos,<br>" . htmlspecialchars($settings['store_name'] ?? 'Loja Online') . "</p>";

            $email_sent = send_email(
                $original_message['email'],
                $original_message['name'],
                $email_subject,
                $email_body_html
            );

            if ($email_sent) {
                add_flash_message('Resposta enviada com sucesso.', 'success');
            } else {
                add_flash_message('Resposta guardada, mas ocorreu um erro ao enviar o email.', 'warning');
            }
        } else {
            add_flash_message('Erro ao guardar a resposta.', 'danger');
        }

        header('Location: admin.php?section=orders&action=detail&id=' . $order_id . '&' . get_session_id_param());
        exit;
    }

    elseif ($section === 'orders' && $action === 'detail' && isset($_POST['anonymize_order'])) {
        $order_id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT) ?: $item_id;

        if (!$order_id) {
            add_flash_message('ID da encomenda inválido.', 'danger');
            header('Location: admin.php?section=orders&' . get_session_id_param());
            exit;
        }

        require_once __DIR__ . '/includes/order_functions.php';

        $notify_customer = isset($_POST['notify_anonymization']) && $_POST['notify_anonymization'] == '1';

        if (anonymize_order($order_id, $notify_customer)) {
            $notification_msg = $notify_customer ? ' Cliente notificado por email.' : '';
            add_flash_message('Dados do cliente anonimizados com sucesso.' . $notification_msg, 'success');
        } else {
            add_flash_message('Erro ao anonimizar os dados do cliente.', 'danger');
        }

        header('Location: admin.php?section=orders&action=detail&id=' . $order_id . '&' . get_session_id_param());
        exit;
    }

    elseif ($section === 'orders' && isset($_POST['delete_order'])) {
        $order_id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);

        if (!$order_id) {
            add_flash_message('ID da encomenda inválido.', 'danger');
            header('Location: admin.php?section=orders&' . get_session_id_param());
            exit;
        }

        require_once __DIR__ . '/includes/order_functions.php';

        if (delete_order($order_id)) {
            add_flash_message('Encomenda eliminada com sucesso.', 'success');
        } else {
            add_flash_message('Erro ao eliminar a encomenda.', 'danger');
        }

        header('Location: admin.php?section=orders&' . get_session_id_param());
        exit;
    }

    elseif ($section === 'orders' && $action === 'detail' && isset($_POST['update_customer_info'])) {
        $order_id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT) ?: $item_id;

        if (!$order_id) {
            add_flash_message('ID da encomenda inválido.', 'danger');
            header('Location: admin.php?section=orders&' . get_session_id_param());
            exit;
        }

        require_once __DIR__ . '/includes/order_functions.php';

        $vat_id = sanitize_input($_POST['customer_vat_id'] ?? '');

        $vat_id = preg_replace('/\D/', '', $vat_id);

        $formatted_vat_id = !empty($vat_id) ? 'PT' . $vat_id : '';

        $customer_data = [
            'customer_name' => sanitize_input($_POST['customer_name'] ?? ''),
            'customer_email' => sanitize_input($_POST['customer_email'] ?? ''),
            'customer_phone' => sanitize_input($_POST['customer_phone'] ?? ''),
            'customer_vat_id' => $formatted_vat_id,
            'shipping_address' => sanitize_input($_POST['shipping_address'] ?? ''),
            'shipping_city' => sanitize_input($_POST['shipping_city'] ?? ''),
            'shipping_zip' => sanitize_input($_POST['shipping_zip'] ?? ''),
            'shipping_country' => sanitize_input($_POST['shipping_country'] ?? ''),
            'order_notes' => sanitize_input($_POST['order_notes'] ?? '')
        ];

        if (isset($_POST['billing_name']) && !empty($_POST['billing_name'])) {
            $customer_data['billing_name'] = sanitize_input($_POST['billing_name']);
            $customer_data['billing_nif'] = sanitize_input($_POST['billing_nif'] ?? '');
            $customer_data['billing_address'] = sanitize_input($_POST['billing_address'] ?? '');
            $customer_data['billing_city'] = sanitize_input($_POST['billing_city'] ?? '');
            $customer_data['billing_zip'] = sanitize_input($_POST['billing_zip'] ?? '');
            $customer_data['billing_country'] = sanitize_input($_POST['billing_country'] ?? '');
        }

        if (update_order_customer_info($order_id, $customer_data)) {
            add_flash_message('Dados do cliente atualizados com sucesso.', 'success');
        } else {
            add_flash_message('Erro ao atualizar os dados do cliente.', 'danger');
        }

        header('Location: admin.php?section=orders&action=detail&id=' . $order_id . '&' . get_session_id_param());
        exit;
    }

    elseif ($section === 'orders' && $action === 'regenerate_token') {
        header('Content-Type: application/json');
        try {
            $order_id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT) ?: $item_id;

            $submitted_csrf = $_POST['csrf_token'] ?? '';
            
            
            $admin_logged_in = isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true;
            
            
            $csrf_valid = validate_csrf_token($submitted_csrf);
            
            
            if (!$csrf_valid) {
                
                
                if (!$admin_logged_in) {
                    echo json_encode(['success' => false, 'message' => 'Erro de segurança (CSRF). Tente novamente.']);
                    exit;
                }
            }

            if (!$order_id) {
                echo json_encode(['success' => false, 'message' => 'ID da encomenda inválido.']);
                exit;
            }

            require_once __DIR__ . '/includes/order_functions.php';

            $order = get_order_by_id($order_id);
            if (!$order) {
                echo json_encode(['success' => false, 'message' => 'Encomenda não encontrada.']);
                exit;
            }

            
            $new_token = create_order_access_token($order_id);
            
            
            $download_tokens_regenerated = false;
            $licenses = db_query(
                "SELECT * FROM licenses WHERE order_id = :order_id",
                [':order_id' => $order_id],
                false, true
            );
            
            if (!empty($licenses)) {
                require_once __DIR__ . '/includes/digital_product_functions.php';

                
                foreach ($licenses as $license) {
                    remove_license_download_tokens($license['id']);
                }
                
                foreach ($licenses as $license) {
                    
                    $new_download_token = create_download_token(
                        $license['id'],
                        60 * 24 * get_setting('digital_direct_download_link_expiry_days', 7), 
                        true 
                    );
                    
                    if ($new_download_token) {
                        $download_tokens_regenerated = true;
                    } else {
                        
                        
                    }
                }
            }

            if ($new_token) {
                $secure_order_url = BASE_URL . '/index.php?view=order_success&token=' . $new_token;
                $response_message = 'Token regenerado com sucesso!';
                
                if ($download_tokens_regenerated) {
                    $response_message .= ' Tokens de download também foram regenerados.';
                }
                
                echo json_encode([
                    'success' => true, 
                    'token' => $new_token, 
                    'url' => $secure_order_url,
                    'message' => $response_message
                ]);
            } else {
                echo json_encode(['success' => false, 'message' => 'Erro ao gerar novo token de acesso. Tente novamente.']);
            }
        } catch (Exception $e) {
            $error_details = [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'order_id' => $order_id ?? 'unknown'
            ];
            
            
            echo json_encode([
                'success' => false, 
                'message' => 'Erro detalhado: ' . $e->getMessage() . ' (Linha: ' . $e->getLine() . ')',
                'debug_info' => $error_details
            ]);
        } catch (Error $e) {
            $error_details = [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'order_id' => $order_id ?? 'unknown'
            ];
            
            
            echo json_encode([
                'success' => false, 
                'message' => 'Erro fatal: ' . $e->getMessage() . ' (Linha: ' . $e->getLine() . ')',
                'debug_info' => $error_details
            ]);
        }
        exit;
    }
    elseif ($section === 'orders' && $action === 'migrate_tokens') {

        $submitted_csrf = $_POST['csrf_token'] ?? '';
        if (!validate_csrf_token($submitted_csrf)) {
            add_flash_message('Erro de segurança (CSRF). Tente novamente.', 'danger');
            header('Location: admin.php?section=maintenance&' . get_session_id_param());
            exit;
        }

        require_once __DIR__ . '/includes/order_functions.php';

        $migration_results = migrate_order_tokens_to_new_format();

        if ($migration_results['success']) {
            $message = 'Migração concluída com sucesso! ';
            $message .= 'Total de tokens: ' . $migration_results['total'] . ', ';
            $message .= 'Tokens migrados: ' . $migration_results['migrated'] . ', ';
            $message .= 'Tokens já migrados: ' . $migration_results['already_migrated'] . ', ';
            $message .= 'Erros: ' . $migration_results['errors'];
            add_flash_message($message, 'success');
        } else {
            add_flash_message($migration_results['message'] ?? 'Erro na migração de tokens.', 'danger');
        }

        header('Location: admin.php?section=maintenance&' . get_session_id_param());
        exit;
    }

    elseif ($section === 'maintenance') {

        $submitted_csrf = $_POST['csrf_token'] ?? '';
        if (!validate_csrf_token($submitted_csrf)) {
            add_flash_message('Erro de segurança (CSRF). Tente novamente.', 'danger');
            header('Location: admin.php?section=maintenance&' . get_session_id_param());
            exit;
        }

        require_once __DIR__ . '/includes/maintenance_functions.php';

        if ($action === 'cleanup_expired_download_tokens') {
            $only_used = isset($_POST['only_used']) && $_POST['only_used'] === '1';
            $result = cleanup_expired_download_tokens($only_used);

            if ($result['success']) {
                add_flash_message($result['message'], 'success');
            } else {
                add_flash_message($result['message'], 'danger');
            }

            header('Location: admin.php?section=maintenance&' . get_session_id_param());
            exit;
        }
        elseif ($action === 'cleanup_expired_order_access_tokens') {
            $result = cleanup_expired_order_access_tokens();

            if ($result['success']) {
                add_flash_message($result['message'], 'success');
            } else {
                add_flash_message($result['message'], 'danger');
            }

            header('Location: admin.php?section=maintenance&' . get_session_id_param());
            exit;
        }
        elseif ($action === 'cleanup_all_active_download_tokens') {
            $result = cleanup_all_active_download_tokens();

            if ($result['success']) {
                add_flash_message($result['message'], 'success');
            } else {
                add_flash_message($result['message'], 'danger');
            }

            header('Location: admin.php?section=maintenance&' . get_session_id_param());
            exit;
        }
        elseif ($action === 'cleanup_all_active_order_access_tokens') {
            $result = cleanup_all_active_order_access_tokens();

            if ($result['success']) {
                add_flash_message($result['message'], 'success');
            } else {
                add_flash_message($result['message'], 'danger');
            }

            header('Location: admin.php?section=maintenance&' . get_session_id_param());
            exit;
        }
        elseif ($action === 'optimize_database') {
            $result = optimize_database();

            if ($result['success']) {
                add_flash_message($result['message'], 'success');
            } else {
                add_flash_message($result['message'], 'danger');
            }

            header('Location: admin.php?section=maintenance&' . get_session_id_param());
            exit;
        }
        elseif ($action === 'check_database_integrity') {
            $result = check_database_integrity();

            if ($result['success']) {
                if ($result['integrity_ok']) {
                    add_flash_message($result['message'], 'success');
                } else {
                    add_flash_message($result['message'], 'warning');
                }
            } else {
                add_flash_message($result['message'], 'danger');
            }

            header('Location: admin.php?section=maintenance&' . get_session_id_param());
            exit;
        }
        elseif ($action === 'backup_database') {
            $backup_dir = '../backups';
            $result = backup_database($backup_dir);

            if ($result['success']) {
                $message = $result['message'];
                if (isset($result['backup_file'])) {
                    $backupFilename = basename($result['backup_file']);
                    $message .= ' <a href="download_backup.php?file=' . urlencode($backupFilename) . '&' . get_session_id_param() . '" class="btn btn-sm btn-primary"><i class="bi bi-download"></i> Download Backup</a>';
                }
                add_flash_message($message, 'success');
            } else {
                add_flash_message($result['message'], 'danger');
            }

            header('Location: admin.php?section=maintenance&' . get_session_id_param());
            exit;
        }
        elseif ($action === 'cleanup_old_order_visits') {
            require_once __DIR__ . '/includes/order_functions.php';

            $days_to_keep = isset($_POST['days_to_keep']) ? (int)$_POST['days_to_keep'] : 30;
            if ($days_to_keep < 1) {
                $days_to_keep = 30;
            }

            $result = cleanup_old_order_visits($days_to_keep);

            if ($result['success']) {
                $message = "Limpeza concluída com sucesso! " . $result['deleted_count'] . " visitas antigas foram removidas.";
                add_flash_message($message, 'success');
            } else {
                add_flash_message($result['message'], 'danger');
            }

            header('Location: admin.php?section=maintenance&' . get_session_id_param());
            exit;
        }
        elseif ($action === 'cleanup_expired_license_verification_tokens') {
            $result = cleanup_expired_license_verification_tokens();

            if ($result['success']) {
                $message = "Limpeza concluída com sucesso! " . $result['deleted_count'] . " tokens de verificação expirados foram removidos.";
                add_flash_message($message, 'success');
            } else {
                add_flash_message($result['message'], 'danger');
            }

            header('Location: admin.php?section=maintenance&' . get_session_id_param());
            exit;
        }
        elseif ($action === 'cleanup_all_license_verification_tokens') {
            $result = cleanup_all_license_verification_tokens();

            if ($result['success']) {
                $message = "Limpeza concluída com sucesso! " . $result['deleted_count'] . " tokens de verificação foram removidos.";
                add_flash_message($message, 'success');
            } else {
                add_flash_message($result['message'], 'danger');
            }

            header('Location: admin.php?section=maintenance&' . get_session_id_param());
            exit;
        }
        elseif ($action === 'cleanup_old_used_license_verification_tokens') {
            $result = cleanup_old_used_license_verification_tokens();

            if ($result['success']) {
                $message = "Limpeza concluída com sucesso! " . $result['deleted_count'] . " tokens de verificação antigos utilizados foram removidos.";
                add_flash_message($message, 'success');
            } else {
                add_flash_message($result['message'], 'danger');
            }

            header('Location: admin.php?section=maintenance&' . get_session_id_param());
            exit;
        }
    }
    elseif ($section === 'orders' && $action === 'detail' && isset($_POST['update_payment_method'])) {
        $order_id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT) ?: $item_id;

        if (!$order_id) {
            add_flash_message('ID da encomenda inválido.', 'danger');
            header('Location: admin.php?section=orders&' . get_session_id_param());
            exit;
        }

        require_once __DIR__ . '/includes/order_functions.php';

        $payment_method = sanitize_input($_POST['payment_method'] ?? '');

        if (update_order_payment_method($order_id, $payment_method)) {
            add_flash_message('Método de pagamento atualizado com sucesso.', 'success');
        } else {
            add_flash_message('Erro ao atualizar o método de pagamento.', 'danger');
        }

        header('Location: admin.php?section=orders&action=detail&id=' . $order_id . '&' . get_session_id_param());
        exit;
    }

    elseif ($section === 'orders' && $action === 'detail' && isset($_POST['resend_order_details'])) {
        $order_id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT) ?: $item_id;

        if (!$order_id) {
            add_flash_message('ID da encomenda inválido.', 'danger');
            header('Location: admin.php?section=orders&' . get_session_id_param());
            exit;
        }

        require_once __DIR__ . '/includes/order_functions.php';

        if (resend_order_details($order_id)) {
            add_flash_message('Detalhes da encomenda reenviados com sucesso.', 'success');
        } else {
            add_flash_message('Erro ao reenviar os detalhes da encomenda.', 'danger');
        }

        header('Location: admin.php?section=orders&action=detail&id=' . $order_id . '&' . get_session_id_param());
        exit;
    }

    elseif ($section === 'coupons' && ($action === 'new' || ($action === 'edit' && $item_id))) {

        $code = strtoupper(trim($_POST['code'] ?? ''));
        $description = trim($_POST['description'] ?? '');
        $discount_type = $_POST['discount_type'] ?? 'percentage';
        $discount_value = filter_var($_POST['discount_value'] ?? 0, FILTER_VALIDATE_FLOAT);
        $min_order_value = filter_var($_POST['min_order_value'] ?? 0, FILTER_VALIDATE_FLOAT);
        $usage_limit = isset($_POST['usage_limit']) && $_POST['usage_limit'] !== '' ? filter_var($_POST['usage_limit'], FILTER_VALIDATE_INT) : null;
        $is_active = isset($_POST['is_active']) && $_POST['is_active'] == '1' ? 1 : 0;
        $start_date = !empty($_POST['start_date']) ? $_POST['start_date'] : null;
        $end_date = !empty($_POST['end_date']) ? $_POST['end_date'] : null;

        $errors = [];
        if (empty($code)) $errors[] = "Código do cupão é obrigatório.";
        if ($discount_value === false || $discount_value < 0) $errors[] = "Valor do desconto inválido.";
        if ($min_order_value === false || $min_order_value < 0) $errors[] = "Valor mínimo de encomenda inválido.";
        if ($usage_limit !== null && ($usage_limit === false || $usage_limit < 1)) $errors[] = "Limite de utilização inválido.";

        $code_check_sql = "SELECT id FROM coupons WHERE code = :code";
        $code_check_params = [':code' => $code];
        if ($action === 'edit') {
            $code_check_sql .= " AND id != :id";
            $code_check_params[':id'] = $item_id;
        }
        $existing_code = db_query($code_check_sql, $code_check_params, true);
        if ($existing_code) $errors[] = "O código '$code' já está em uso. Escolha outro.";

        if (!empty($start_date) && !empty($end_date)) {
            $start_timestamp = strtotime($start_date);
            $end_timestamp = strtotime($end_date);
            if ($start_timestamp && $end_timestamp && $end_timestamp < $start_timestamp) {
                $errors[] = "A data de fim deve ser posterior à data de início.";
            }
        }

        if (!empty($errors)) {
            foreach ($errors as $error) add_flash_message($error, 'danger');
            $_SESSION['form_data'] = $_POST;
            $redirect_url = 'admin.php?section=coupons&action=' . $action;
            if ($item_id) $redirect_url .= '&id=' . $item_id;
            header('Location: ' . $redirect_url . '&' . get_session_id_param());
            exit;
        }

        try {
            $coupon_data = [
                'code' => $code,
                'description' => $description,
                'discount_type' => $discount_type,
                'discount_value' => $discount_value,
                'min_order_value' => $min_order_value,
                'usage_limit' => $usage_limit,
                'is_active' => $is_active,
                'start_date' => $start_date,
                'end_date' => $end_date
            ];

            if ($action === 'new') {
                $new_id = create_coupon($coupon_data);
                if ($new_id) {
                    add_flash_message('Cupão criado com sucesso!', 'success');

                    if (isset($_POST['save_and_continue'])) {
                        header('Location: admin.php?section=coupons&action=edit&id=' . $new_id . '&' . get_session_id_param());
                    } else {
                        header('Location: admin.php?section=coupons&' . get_session_id_param());
                    }
                    exit;
                } else {
                    throw new Exception("Falha ao criar cupão.");
                }
            } else {
                $updated = update_coupon($item_id, $coupon_data);
                if ($updated) {
                    add_flash_message('Cupão atualizado com sucesso!', 'success');

                    if (isset($_POST['save_and_continue'])) {
                        header('Location: admin.php?section=coupons&action=edit&id=' . $item_id . '&' . get_session_id_param());
                    } else {
                        header('Location: admin.php?section=coupons&' . get_session_id_param());
                    }
                    exit;
                } else {

                    if (!get_coupon_by_id($item_id)) {
                        throw new Exception("Cupão não encontrado para atualização (ID: $item_id).");
                    } else {
                        throw new Exception("Falha ao atualizar cupão.");
                    }
                }
            }
        } catch (Exception $e) {
            add_flash_message('Erro ao guardar cupão: ' . $e->getMessage(), 'danger');
            $_SESSION['form_data'] = $_POST;
            $redirect_url = 'admin.php?section=coupons&action=' . $action;
            if ($item_id) $redirect_url .= '&id=' . $item_id;
            header('Location: ' . $redirect_url . '&' . get_session_id_param());
            exit;
        }
    }

    elseif ($section === 'payment_methods' && (($action === 'create' || $action === 'new') || ($action === 'update' && $item_id))) {

        ensure_payment_methods_table_exists();

        $title = trim($_POST['title'] ?? '');
        $instructions = trim($_POST['instructions'] ?? '');
        $is_active = isset($_POST['is_active']) ? (int)$_POST['is_active'] : 1;
        $sort_order = isset($_POST['sort_order']) ? (int)$_POST['sort_order'] : 0;

        $errors = [];
        if (empty($title)) $errors[] = "O título do método de pagamento é obrigatório.";
        if (empty($instructions)) $errors[] = "As instruções de pagamento são obrigatórias.";

        if (!empty($errors)) {
            foreach ($errors as $error) add_flash_message($error, 'danger');
            $_SESSION['form_data'] = $_POST;
            $redirect_url = 'admin.php?section=payment_methods&action=';
            $redirect_url .= ($action === 'create' || $action === 'new') ? 'new' : 'edit&id=' . $item_id;
            header('Location: ' . $redirect_url . '&' . get_session_id_param());
            exit;
        }

        $payment_data = [
            'title' => $title,
            'instructions' => $instructions,
            'is_active' => $is_active,
            'sort_order' => $sort_order
        ];

        try {
            if ($action === 'create' || $action === 'new') {
                $new_id = add_payment_method($payment_data);
                if ($new_id) {
                    add_flash_message('Método de pagamento adicionado com sucesso!', 'success');
                    header('Location: admin.php?section=payment_methods&' . get_session_id_param());
                    exit;
                } else {
                    throw new Exception("Falha ao adicionar método de pagamento.");
                }
            } else {
                $updated = update_payment_method($item_id, $payment_data);
                if ($updated) {
                    add_flash_message('Método de pagamento atualizado com sucesso!', 'success');
                    header('Location: admin.php?section=payment_methods&' . get_session_id_param());
                    exit;
                } else {
                    throw new Exception("Falha ao atualizar método de pagamento.");
                }
            }
        } catch (Exception $e) {
            add_flash_message('Erro ao guardar método de pagamento: ' . $e->getMessage(), 'danger');
            $_SESSION['form_data'] = $_POST;
            $redirect_url = 'admin.php?section=payment_methods&action=';
            $redirect_url .= ($action === 'create' || $action === 'new') ? 'new' : 'edit&id=' . $item_id;
            header('Location: ' . $redirect_url . '&' . get_session_id_param());
            exit;
        }
    }

    elseif (isset($_POST['action']) && $_POST['action'] === 'delete_message') {

        if (ob_get_level()) ob_clean();

        header('Content-Type: application/json');
        $response = ['success' => false, 'message' => 'Erro desconhecido.'];

        $message_id = filter_input(INPUT_POST, 'message_id', FILTER_VALIDATE_INT);

        if (!$message_id) {
            $response['message'] = 'ID da mensagem inválido.';
            if (ob_get_level()) ob_clean();
            echo @json_encode($response);
            exit;
        }

        $pdo = get_db_connection();
        if (!$pdo) {
             $response['message'] = 'Erro ao conectar à base de dados.';
             if (ob_get_level()) ob_clean();
             echo @json_encode($response);
             exit;
        }

        try {
            $pdo->beginTransaction();

            $delete_replies_sql = "DELETE FROM message_replies WHERE message_id = :msg_id";
            db_query($delete_replies_sql, [':msg_id' => $message_id]);

            $delete_message_sql = "DELETE FROM contacts WHERE id = :msg_id";
            $deleted_rows = db_query($delete_message_sql, [':msg_id' => $message_id]);

            if ($deleted_rows === false) {
                 throw new Exception('Falha ao executar a query de remoção da mensagem.');
            } elseif ($deleted_rows === 0) {

            }

            $pdo->commit();
            $response['success'] = true;
            $response['message'] = 'Mensagem removida com sucesso.';

        } catch (Exception $e) {
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            $response['success'] = false;
            $response['message'] = 'Erro ao remover mensagem: ' . $e->getMessage();
        }

        if (ob_get_level()) ob_clean();
        echo @json_encode($response);
        exit;
    }

    elseif ($section === 'messages' && $action === 'delete' && isset($_POST['message_id'])) {

        $message_id = filter_input(INPUT_POST, 'message_id', FILTER_VALIDATE_INT);

        if (!$message_id) {
            add_flash_message('ID da mensagem inválido.', 'danger');
            header('Location: admin.php?section=messages&' . get_session_id_param());
            exit;
        }

        $pdo = get_db_connection();
        if (!$pdo) {
            add_flash_message('Erro ao conectar à base de dados.', 'danger');
            header('Location: admin.php?section=messages&' . get_session_id_param());
            exit;
        }

        try {
            $pdo->beginTransaction();

            $delete_replies_sql = "DELETE FROM message_replies WHERE message_id = :msg_id";
            db_query($delete_replies_sql, [':msg_id' => $message_id]);

            $delete_message_sql = "DELETE FROM contacts WHERE id = :msg_id";
            $deleted_rows = db_query($delete_message_sql, [':msg_id' => $message_id]);

            if ($deleted_rows === false) {
                throw new Exception('Falha ao executar a query de remoção da mensagem.');
            }

            $pdo->commit();
            add_flash_message('Mensagem removida com sucesso.', 'success');

        } catch (Exception $e) {
            if ($pdo->inTransaction()) {
                $pdo->rollBack();
            }
            add_flash_message('Erro ao remover mensagem: ' . $e->getMessage(), 'danger');
        }

        header('Location: admin.php?section=messages&' . get_session_id_param());
        exit;
    }

    elseif ($section === 'sessions' && $action === 'delete_session' && isset($_POST['session_id_to_delete'])) {
        $session_id_to_delete = $_POST['session_id_to_delete'];

        

        $result = delete_session_and_associated_tokens($session_id_to_delete);

        if ($result['success']) {
            add_flash_message($result['message'], 'success');
        } else {
            add_flash_message($result['message'], 'danger');
        }
        header('Location: admin.php?section=sessions&' . get_session_id_param());
        exit;
    }

    elseif ($section === 'sessions' && $action === 'bulk_delete' && isset($_POST['session_ids'])) {
        if (!validate_csrf_token($_POST['csrf_token'] ?? '')) {
            add_flash_message('Erro de segurança (CSRF). Ação cancelada.', 'danger');
        } else {
            $session_ids = $_POST['session_ids'];
            $session_ids_array = array_filter(array_map('trim', explode(',', $session_ids)));
            
            if (empty($session_ids_array)) {
                add_flash_message('Nenhuma sessão selecionada para eliminar.', 'warning');
            } else {
                $success_count = 0;
                $error_count = 0;
                $errors = [];
                
                foreach ($session_ids_array as $session_id) {
                    $result = delete_session_and_associated_tokens($session_id);
                    if ($result['success']) {
                        $success_count++;
                    } else {
                        $error_count++;
                        $errors[] = $result['message'];
                    }
                }
                
                if ($success_count > 0) {
                    $message = "Eliminadas {$success_count} sessão" . ($success_count > 1 ? 'ões' : '') . " com sucesso.";
                    if ($error_count > 0) {
                        $message .= " {$error_count} sessão" . ($error_count > 1 ? 'ões' : '') . " não puderam ser eliminadas.";
                    }
                    add_flash_message($message, $error_count > 0 ? 'warning' : 'success');
                } else {
                    add_flash_message('Erro ao eliminar as sessões selecionadas.', 'danger');
                }
            }
        }
        header('Location: admin.php?section=sessions&' . get_session_id_param());
        exit;
    }

    elseif ($section === 'sessions' && $action === 'delete_expired_empty_sessions') {
        if (!validate_csrf_token($_POST['csrf_token'] ?? '')) {
            add_flash_message('Erro de segurança (CSRF). Ação cancelada.', 'danger');
        } else {
            $result = delete_expired_empty_sessions_action();
            if ($result['success']) {
                add_flash_message($result['message'], 'success');
            } else {
                add_flash_message($result['message'], 'danger');
            }
        }
        header('Location: admin.php?section=sessions&' . get_session_id_param());
        exit;
    }

    elseif ($section === 'sessions' && $action === 'delete_non_portuguese_sessions') {
        if (!validate_csrf_token($_POST['csrf_token'] ?? '')) {
            add_flash_message('Erro de segurança (CSRF). Ação cancelada.', 'danger');
        } else {
            $result = delete_non_portuguese_sessions_action();
            if ($result['success']) {
                add_flash_message($result['message'], 'success');
            } else {
                add_flash_message($result['message'], 'danger');
            }
        }
        header('Location: admin.php?section=sessions&' . get_session_id_param());
        exit;
    }

    elseif ($section === 'blog_categories' && ($action === 'new' || ($action === 'edit' && $item_id))) {
        $name = trim($_POST['name'] ?? '');
        $slug_input = trim($_POST['slug'] ?? '');
        $description = trim($_POST['description'] ?? '');
        $is_active = isset($_POST['is_active']) && $_POST['is_active'] == '1' ? 1 : 0;

        $errors = [];
        if (empty($name)) $errors[] = "Nome da categoria é obrigatório.";

        $slug = !empty($slug_input) ? generate_slug($slug_input) : generate_slug($name);

        $slug_check_sql = "SELECT id FROM blog_categories WHERE slug = :slug";
        $slug_check_params = [':slug' => $slug];
        if ($action === 'edit') {
            $slug_check_sql .= " AND id != :id";
            $slug_check_params[':id'] = $item_id;
        }
        $existing_slug = db_query($slug_check_sql, $slug_check_params, true);
        if ($existing_slug) $errors[] = "O slug '$slug' já está em uso.";

        $name_check_sql = "SELECT id FROM blog_categories WHERE name = :name";
        $name_check_params = [':name' => $name];
        if ($action === 'edit') {
            $name_check_sql .= " AND id != :id";
            $name_check_params[':id'] = $item_id;
        }
        $existing_name = db_query($name_check_sql, $name_check_params, true);
        if ($existing_name) $errors[] = "O nome da categoria '$name' já existe.";

        if (!empty($errors)) {
            foreach ($errors as $error) add_flash_message($error, 'danger');
            $_SESSION['form_data'] = $_POST;
            $redirect_url = 'admin.php?section=blog_categories&action=' . $action;
            if ($item_id) $redirect_url .= '&id=' . $item_id;
            header('Location: ' . $redirect_url . '&' . get_session_id_param());
            exit;
        }

        $category_data = [
            'name' => $name,
            'description' => $description,
            'is_active' => $is_active,
            'slug' => $slug
        ];

        try {
            if ($action === 'new') {
                $new_id = add_blog_category($category_data);
                if ($new_id) {
                    add_flash_message('Categoria de blog adicionada com sucesso!', 'success');
                } else {
                    throw new Exception("Falha ao adicionar categoria de blog.");
                }
            } else {
                $updated = update_blog_category($item_id, $category_data);
                if ($updated) {
                    add_flash_message('Categoria de blog atualizada com sucesso!', 'success');
                } else {
                    throw new Exception("Falha ao atualizar categoria de blog.");
                }
            }
            header('Location: admin.php?section=blog_categories&' . get_session_id_param());
            exit;
        } catch (Exception $e) {
            add_flash_message('Erro ao guardar categoria de blog: ' . $e->getMessage(), 'danger');
            $_SESSION['form_data'] = $_POST;
            $redirect_url = 'admin.php?section=blog_categories&action=' . $action;
            if ($item_id) $redirect_url .= '&id=' . $item_id;
            header('Location: ' . $redirect_url . '&' . get_session_id_param());
            exit;
        }
    }

    elseif ($section === 'blog_posts' && ($action === 'new' || ($action === 'edit' && $item_id))) {
        $pdo = get_db_connection();
        if (!$pdo) {
             add_flash_message('Erro crítico: Falha na ligação à base de dados.', 'danger');
             header('Location: admin.php?section=blog_posts&' . get_session_id_param());
             exit;
        }

        $is_published_submitted = isset($_POST['is_published_submitted']);
        $is_published = isset($_POST['is_published']) && $_POST['is_published'] == '1' ? 1 : 0;

        $post_data = [
            'title' => trim($_POST['title'] ?? ''),
            'slug' => trim($_POST['slug'] ?? ''),
            'post_type' => $_POST['post_type'] ?? 'article',
            'content' => $_POST['content'] ?? '',
            'link_url' => trim($_POST['link_url'] ?? ''),
            'link_description' => trim($_POST['link_description'] ?? ''),
            'code_content' => $_POST['code_content'] ?? '', 
            'category_ids' => $_POST['category_ids'] ?? [],
            'is_published' => $is_published,
            'published_at' => !empty($_POST['published_at']) ? date('Y-m-d H:i:s', strtotime($_POST['published_at'])) : null,
            'image_description' => trim($_POST['image_description'] ?? ''),
            'seo_title' => trim($_POST['seo_title'] ?? ''),
            'seo_description' => trim($_POST['seo_description'] ?? ''),
            'seo_keywords' => trim($_POST['seo_keywords'] ?? ''),
            'og_title' => trim($_POST['og_title'] ?? ''),
            'og_description' => trim($_POST['og_description'] ?? ''),
            'og_image' => trim($_POST['og_image'] ?? ''),
            'twitter_card' => trim($_POST['twitter_card'] ?? 'summary_large_image'),
            'twitter_title' => trim($_POST['twitter_title'] ?? ''),
            'twitter_description' => trim($_POST['twitter_description'] ?? ''),
            'twitter_image' => trim($_POST['twitter_image'] ?? ''),

        ];

        $errors = [];
        if (empty($post_data['title'])) $errors[] = "Título do post é obrigatório.";
        if (!in_array($post_data['post_type'], ['article', 'link', 'CODE'])) $errors[] = "Tipo de post inválido.";
        if ($post_data['post_type'] === 'link' && empty($post_data['link_url'])) $errors[] = "URL do link é obrigatório para posts do tipo Link.";
        if ($post_data['post_type'] === 'CODE' && empty($post_data['code_content'])) $errors[] = "Conteúdo de código é obrigatório para posts do tipo Código Executável.";
        if (empty($post_data['category_ids'])) $errors[] = "Selecione pelo menos uma categoria.";

        if (empty($post_data['slug'])) {
            $post_data['slug'] = generate_slug($post_data['title']);
        } else {
            $post_data['slug'] = generate_slug($post_data['slug']);
        }
        $slug_check_sql = "SELECT id FROM blog_posts WHERE slug = :slug";
        $slug_check_params = [':slug' => $post_data['slug']];
        if ($action === 'edit') {
            $slug_check_sql .= " AND id != :id";
            $slug_check_params[':id'] = $item_id;
        }
        $existing_slug = db_query($slug_check_sql, $slug_check_params, true);
        if ($existing_slug) $errors[] = "O slug '{$post_data['slug']}' já está em uso.";

        $image_path = null;
        $current_image_path = null;
        if ($action === 'edit') {
            $current_post = get_blog_post($item_id);
            $current_image_path = $current_post['image_path'] ?? null;
        }
        $delete_current_image = isset($_POST['delete_current_image']) && $_POST['delete_current_image'] == '1';

        if (isset($_FILES['image_path']) && $_FILES['image_path']['error'] === UPLOAD_ERR_OK) {

            $upload_dir = __DIR__ . '/public/uploads/blog/';
            if (!is_dir($upload_dir)) mkdir($upload_dir, 0777, true);
            $allowed_types = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
            $max_file_size = 5 * 1024 * 1024;

            $tmp_name = $_FILES['image_path']['tmp_name'];
            $original_name = $_FILES['image_path']['name'];
            $file_size = $_FILES['image_path']['size'];
            $file_type = $_FILES['image_path']['type'];

            if (!in_array($file_type, $allowed_types)) {
                $errors[] = "Tipo de ficheiro inválido para a imagem. Permitidos: JPG, PNG, GIF, WEBP.";
            } elseif ($file_size > $max_file_size) {
                $errors[] = "Ficheiro de imagem demasiado grande (Máx: " . ($max_file_size / 1024 / 1024) . " MB).";
            } else {
                $file_extension = pathinfo($original_name, PATHINFO_EXTENSION);
                $safe_filename = 'post_' . ($item_id ?? 'new') . '_' . time() . '.' . $file_extension;
                $destination = $upload_dir . $safe_filename;

                if (move_uploaded_file($tmp_name, $destination)) {
                    $image_path = 'public/uploads/blog/' . $safe_filename;

                    if ($current_image_path && file_exists(__DIR__ . '/' . $current_image_path)) {
                        @unlink(__DIR__ . '/' . $current_image_path);
                    }
                } else {
                    $errors[] = "Erro ao mover o ficheiro da imagem carregada.";
                }
            }
        } elseif ($delete_current_image && $current_image_path) {

            if (file_exists(__DIR__ . '/' . $current_image_path)) {
                @unlink(__DIR__ . '/' . $current_image_path);
            }
            $image_path = null;
        } elseif ($current_image_path) {

            $image_path = $current_image_path;
        }

        if (empty($image_path)) {
            $errors[] = "É necessário carregar uma imagem para o post.";
        }

        
        if ($post_data['post_type'] === 'CODE') {
            $post_data['content'] = null;
            $post_data['link_url'] = null;
            
        } elseif ($post_data['post_type'] === 'article') {
            $post_data['link_url'] = null;
            $post_data['link_description'] = null;
            $post_data['code_content'] = null;
        } elseif ($post_data['post_type'] === 'link') {
            $post_data['content'] = null;
            $post_data['code_content'] = null;
            
        }

        if (!empty($errors)) {
            foreach ($errors as $error) add_flash_message($error, 'danger');
            $_SESSION['form_data'] = $_POST;
            $redirect_url = 'admin.php?section=blog_posts&action=' . $action;
            if ($item_id) $redirect_url .= '&id=' . $item_id;
            header('Location: ' . $redirect_url . '&' . get_session_id_param());
            exit;
        }

        $post_data['image_path'] = $image_path;

        function normalize_image_url($url) {

            if (empty($url) || preg_match('/^https?:\/\//i', $url)) {
                return $url;
            }

            if (strpos($url, 'public/') === 0) {

                return $url;
            }

            return $url;
        }

        if (empty($post_data['og_image'])) {
            $post_data['og_image'] = $image_path;
        } else {
            $post_data['og_image'] = normalize_image_url($post_data['og_image']);
        }

        if (empty($post_data['twitter_image'])) {
            $post_data['twitter_image'] = $image_path;
        } else {
            $post_data['twitter_image'] = normalize_image_url($post_data['twitter_image']);
        }

        try {
            if ($action === 'new') {
                $new_id = add_blog_post($post_data);
                if ($new_id) {
                    add_flash_message('Post de blog adicionado com sucesso!', 'success');
                    $item_id = $new_id;
                } else {
                    throw new Exception("Falha ao adicionar post de blog.");
                }
            } else {
                $updated = update_blog_post($item_id, $post_data);
                if ($updated) {
                    add_flash_message('Post de blog atualizado com sucesso!', 'success');
                } else {
                    throw new Exception("Falha ao atualizar post de blog.");
                }
            }

            $save_action = $_POST['save_action'] ?? 'save_and_return';
            if ($save_action === 'save_and_continue') {
                header('Location: admin.php?section=blog_posts&action=edit&id=' . $item_id . '&' . get_session_id_param());
            } else {
                header('Location: admin.php?section=blog_posts&' . get_session_id_param());
            }
            exit;

        } catch (Exception $e) {
            add_flash_message('Erro ao guardar post de blog: ' . $e->getMessage(), 'danger');

            if ($action === 'new' && !empty($image_path) && file_exists(__DIR__ . '/' . $image_path)) {
                @unlink(__DIR__ . '/' . $image_path);
            }
            $_SESSION['form_data'] = $_POST;
            $redirect_url = 'admin.php?section=blog_posts&action=' . $action;
            if ($item_id) $redirect_url .= '&id=' . $item_id;
            header('Location: ' . $redirect_url . '&' . get_session_id_param());
            exit;
        }
    }

    elseif ($section === 'licenses' && $action === 'update_expiry' && $item_id && isset($_POST['expiry_date'])) {

        require_once __DIR__ . '/includes/digital_product_functions.php';
        $expiry_date = sanitize_input($_POST['expiry_date'] ?? '');

        if (empty($expiry_date)) {
            add_flash_message('Data de expiração inválida.', 'danger');
        } else if (update_license_expiry_date($item_id, $expiry_date)) {
            add_flash_message('Data de expiração atualizada com sucesso.', 'success');
        } else {
            add_flash_message('Erro ao atualizar data de expiração.', 'danger');
        }

        $return_to = filter_input(INPUT_GET, 'return_to', FILTER_SANITIZE_STRING);
        $redirect_url = ($return_to === 'detail')
            ? "admin.php?section=licenses&action=detail&id={$item_id}"
            : "admin.php?section=licenses";

        $final_redirect_url = add_session_param_to_url($redirect_url);
        header("Location: " . $final_redirect_url);
        exit;
    }

}

elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'GET' && $section === 'sitemaps') {
    require_once __DIR__ . '/includes/sitemap_functions.php';

    if ($action === 'generate' && $item_id) {
            $result = generate_sitemap((int)$item_id);
            if ($result['success']) {
                add_flash_message("Sitemap ID {$item_id} gerado: " . htmlspecialchars($result['message']), 'success');
            } else {
                add_flash_message("Erro ao gerar sitemap ID {$item_id}: " . htmlspecialchars($result['message']), 'danger');
            }
        
        header('Location: admin.php?section=sitemaps&' . get_session_id_param());
        exit;
    } elseif ($action === 'generate_all') {
        $results = generate_all_active_sitemaps();
        add_flash_message(htmlspecialchars($results['message']), $results['success'] ? 'success' : 'danger');
        
        if (isset($results['details']) && is_array($results['details'])) {
            foreach ($results['details'] as $name => $detail) {
            }
        } else {
        }

        header('Location: admin.php?section=sitemaps&' . get_session_id_param());
        exit;
    }
}
elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'GET' && $section === 'page_categories' && $action === 'delete' && isset($_GET['id'])) {

    $delete_id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

    if ($delete_id) {
        if (delete_page_category($delete_id)) {
            add_flash_message('Categoria de página removida com sucesso.', 'success');
        } else {
            add_flash_message('Erro ao remover categoria de página.', 'danger');
        }
    } else {
        add_flash_message('ID de categoria inválido para remoção.', 'warning');
    }
    header('Location: admin.php?section=page_categories&' . get_session_id_param());
    exit;
}
elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'GET' && $section === 'page_placeholders' && $action === 'delete' && isset($_GET['id'])) {

    $delete_id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

    if ($delete_id) {
        if (delete_page_placeholder($delete_id)) {
            add_flash_message('Placeholder removido com sucesso.', 'success');
        } else {
            add_flash_message('Erro ao remover placeholder.', 'danger');
        }
    } else {
        add_flash_message('ID de placeholder inválido para remoção.', 'warning');
    }
    header('Location: admin.php?section=page_placeholders&' . get_session_id_param());
    exit;
}

elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'GET' && $section === 'placeholder_links' && $action === 'delete' && isset($_GET['id'])) {
    $delete_id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

    if ($delete_id) {
        if (delete_placeholder_link($delete_id)) {
            add_flash_message('Link removido com sucesso.', 'success');
        } else {
            add_flash_message('Erro ao remover link.', 'danger');
        }
    } else {
        add_flash_message('ID de link inválido para remoção.', 'warning');
    }
    header('Location: admin.php?section=placeholder_links&' . get_session_id_param());
    exit;
}
elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'POST' && $section === 'coupons' && $action === 'delete') {

    $coupon_id = filter_input(INPUT_POST, 'coupon_id', FILTER_VALIDATE_INT);

    if ($coupon_id) {
        if (delete_coupon($coupon_id)) {
            add_flash_message('Cupão removido com sucesso.', 'success');
        } else {
            add_flash_message('Erro ao remover cupão.', 'danger');
        }
    } else {
        add_flash_message('ID de cupão inválido para remoção.', 'warning');
    }
    header('Location: admin.php?section=coupons&' . get_session_id_param());
    exit;
}
elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'GET' && $section === 'pages' && $action === 'delete' && isset($_GET['id'])) {

    $delete_id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

    if ($delete_id) {

        if (delete_page($delete_id)) {
            add_flash_message('Página removida com sucesso.', 'success');
        } else {
            add_flash_message('Erro ao remover página.', 'danger');
        }
    } else {
        add_flash_message('ID de página inválido para remoção.', 'warning');
    }
    header('Location: admin.php?section=pages&' . get_session_id_param());
    exit;
}
elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'GET' && $section === 'categories' && $action === 'delete' && isset($_GET['id'])) {

    $delete_id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

    if ($delete_id) {
        if (function_exists('deleteCategory') && deleteCategory($delete_id)) {
            add_flash_message('Categoria de produto removida com sucesso.', 'success');
        } else {
            add_flash_message('Erro ao remover categoria de produto.', 'danger');
        }
    } else {
        add_flash_message('ID de categoria inválido para remoção.', 'warning');
    }
    header('Location: admin.php?section=categories&' . get_session_id_param());
    exit;
}
elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'GET' && $section === 'attributes' && $action === 'delete' && isset($_GET['id'])) {

    $delete_id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

    if ($delete_id) {

        $deleted = db_query("DELETE FROM attributes WHERE id = :id", [':id' => $delete_id]);
        if ($deleted) {
            add_flash_message('Atributo removido com sucesso.', 'success');
        } else {
            add_flash_message('Erro ao remover atributo.', 'danger');
        }
    } else {
        add_flash_message('ID de atributo inválido para remoção.', 'warning');
    }

    header('Location: admin.php?section=attributes&action=list&' . get_session_id_param());
    exit;

}
elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'GET' && $section === 'products' && $action === 'duplicate' && isset($_GET['id'])) {

    $original_product_id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

    if (!$original_product_id) {
        add_flash_message('ID do produto inválido para duplicação.', 'warning');
        header('Location: admin.php?section=products&' . get_session_id_param());
        exit;
    }

    $pdo = get_db_connection();
    if (!$pdo) {
        add_flash_message('Erro crítico: Falha na ligação à base de dados.', 'danger');
        header('Location: admin.php?section=products&' . get_session_id_param());
        exit;
    }

    try {
        $pdo->beginTransaction();

        $original_product = db_query("SELECT * FROM products WHERE id = :id", [':id' => $original_product_id], true);
        if (!$original_product) {
            throw new Exception("Produto original não encontrado (ID: $original_product_id).");
        }

        $new_name = $original_product['name_pt'] . '_duplicado';
        $new_slug_base = generate_slug($new_name);
        $new_slug = $new_slug_base;
        $counter = 1;
        while (db_query("SELECT id FROM products WHERE slug = :slug", [':slug' => $new_slug], true)) {
            $new_slug = $new_slug_base . '-' . $counter++;
        }

        $new_product_data = [
            ':name_pt' => $new_name,
            ':slug' => $new_slug,
            ':description_pt' => $original_product['description_pt'],
            ':base_price' => $original_product['base_price'],
            ':is_active' => 0,

            ':seo_title' => $original_product['seo_title'] ?? '',
            ':seo_description' => $original_product['seo_description'] ?? '',
            ':seo_keywords' => $original_product['seo_keywords'] ?? '',
            ':og_title' => $original_product['og_title'] ?? '',
            ':og_description' => $original_product['og_description'] ?? '',
            ':og_image' => $original_product['og_image'] ?? '',
            ':twitter_card' => $original_product['twitter_card'] ?? 'summary_large_image',
            ':twitter_title' => $original_product['twitter_title'] ?? '',
            ':twitter_description' => $original_product['twitter_description'] ?? '',
            ':twitter_image' => $original_product['twitter_image'] ?? ''
        ];

        $is_simple_product = !empty($original_product['sku']);

        if ($is_simple_product) {

            $new_sku = generate_unique_sku($new_name, true);

            $new_product_data[':sku'] = $new_sku;
            $new_product_data[':stock'] = $original_product['stock'] ?? 0;

            $sql_insert_product = "INSERT INTO products (name_pt, slug, description_pt, base_price, is_active, sku, stock,
                                   seo_title, seo_description, seo_keywords, og_title, og_description, og_image,
                                   twitter_card, twitter_title, twitter_description, twitter_image,
                                   created_at, updated_at)
                                   VALUES (:name_pt, :slug, :description_pt, :base_price, :is_active, :sku, :stock,
                                   :seo_title, :seo_description, :seo_keywords, :og_title, :og_description, :og_image,
                                   :twitter_card, :twitter_title, :twitter_description, :twitter_image,
                                   datetime('now', 'localtime'), datetime('now', 'localtime'))";
        } else {

            $sql_insert_product = "INSERT INTO products (name_pt, slug, description_pt, base_price, is_active,
                                   seo_title, seo_description, seo_keywords, og_title, og_description, og_image,
                                   twitter_card, twitter_title, twitter_description, twitter_image,
                                   created_at, updated_at)
                                   VALUES (:name_pt, :slug, :description_pt, :base_price, :is_active,
                                   :seo_title, :seo_description, :seo_keywords, :og_title, :og_description, :og_image,
                                   :twitter_card, :twitter_title, :twitter_description, :twitter_image,
                                   datetime('now', 'localtime'), datetime('now', 'localtime'))";
        }

        $stmt = db_query($sql_insert_product, $new_product_data, false, false);
        $new_product_id = $pdo->lastInsertId();

        if (!$new_product_id || $new_product_id == 0) {

             throw new Exception("Falha ao inserir o registo do novo produto. A duplicação foi abortada.");
        }

        require_once __DIR__ . '/includes/product_info_fields.php';
        $original_info_fields = get_product_info_fields($original_product_id);
        if ($original_info_fields) {
            foreach ($original_info_fields as $orig_field) {
                add_product_info_field(
                    $new_product_id,
                    $orig_field['icon'],
                    $orig_field['text'],
                    $orig_field['sort_order']
                );
            }
        }
        $original_categories = db_query("SELECT category_id FROM product_categories WHERE product_id = :pid", [':pid' => $original_product_id], false, true);
        if ($original_categories) {
            $sql_insert_cat = "INSERT INTO product_categories (product_id, category_id) VALUES (:pid, :cid)";
            foreach ($original_categories as $cat) {
                db_query($sql_insert_cat, [':pid' => $new_product_id, ':cid' => $cat['category_id']]);
            }
        }

        $pdo->commit();
        add_flash_message('Produto duplicado com sucesso! O novo produto está inativo por defeito.', 'success');

    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        add_flash_message('Erro ao duplicar o produto: ' . $e->getMessage(), 'danger');
    }

    header('Location: admin.php?section=products&' . get_session_id_param());
    exit;

}

elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'GET' && $section === 'products' && $action === 'get_attribute_value_details') {

    header('Content-Type: application/json');
    $attribute_id = filter_input(INPUT_GET, 'attribute_id', FILTER_VALIDATE_INT);
    $value_names_raw = $_GET['value_names'] ?? '';

    if (!$attribute_id || empty($value_names_raw)) {
        echo json_encode(['success' => false, 'message' => 'Attribute ID or values missing.']);
        exit;
    }

    $value_names = array_map('trim', explode(',', $value_names_raw));
    if (empty($value_names)) {
         echo json_encode(['success' => true, 'details' => []]);
         exit;
    }
    $placeholders = implode(',', array_fill(0, count($value_names), '?'));
    $params = array_merge([$attribute_id], $value_names);

    $sql = "SELECT id, value_pt, price_modifier FROM attribute_values WHERE attribute_id = ? AND value_pt IN ($placeholders)";
    $values_details = db_query($sql, $params, false, true);

    $results_by_name = [];
    if ($values_details) {
        foreach ($values_details as $detail) {
            $results_by_name[$detail['value_pt']] = [
                'id' => $detail['id'],
                'modifier' => (float)$detail['price_modifier']
            ];
        }
    }

    echo json_encode(['success' => true, 'details' => $results_by_name]);
    exit;

}
elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'GET' && $section === 'products' && $action === 'get_attribute_values') {

    header('Content-Type: application/json');
    $attribute_id = filter_input(INPUT_GET, 'attribute_id', FILTER_VALIDATE_INT);

    if (!$attribute_id) {
        echo json_encode(['success' => false, 'message' => 'Attribute ID inválido.']);
        exit;
    }

    try {

        $sql = "SELECT id, value_pt, price_modifier FROM attribute_values WHERE attribute_id = :aid ORDER BY value_pt";
        $values = db_query($sql, [':aid' => $attribute_id], false, true);

        if ($values === false) {
             throw new Exception("Erro ao consultar valores do atributo.");
        }

        foreach ($values as &$value) {
            $value['price_modifier'] = (float)$value['price_modifier'];
        }
        unset($value);

        echo json_encode(['success' => true, 'values' => $values ?: []]);

    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Erro ao obter valores do atributo.']);
    }
    exit;

}
elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'GET' && $section === 'messages' && $action === 'get_replies') {

    header('Content-Type: application/json');
    $message_id = filter_input(INPUT_GET, 'message_id', FILTER_VALIDATE_INT);

    if (!$message_id) {
        echo json_encode(['success' => false, 'message' => 'ID da mensagem inválido.']);
        exit;
    }

    try {
        $replies = get_message_replies($message_id);
        echo json_encode(['success' => true, 'replies' => $replies]);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => 'Erro ao obter respostas.']);
    }
    exit;
}

elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'GET' && $section === 'messages' && $action === 'download_message') {

    $message_id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

    $submitted_csrf = $_GET['csrf_token'] ?? '';
    if (!validate_csrf_token($submitted_csrf)) {
        add_flash_message('Erro de segurança (CSRF). Tente novamente.', 'danger');
        header('Location: admin.php?section=messages&' . get_session_id_param());
        exit;
    }

    if (!$message_id) {
        add_flash_message('ID da mensagem inválido.', 'danger');
        header('Location: admin.php?section=messages&' . get_session_id_param());
        exit;
    }

    try {

        $message_history = get_messages_with_replies('created_at', 'DESC');

        if (!isset($message_history[$message_id])) {
            add_flash_message('Mensagem não encontrada.', 'danger');
            header('Location: admin.php?section=messages&' . get_session_id_param());
            exit;
        }

        $message_data = $message_history[$message_id]['message_data'];
        $replies = $message_history[$message_id]['replies'];

        $safe_subject = preg_replace('/[^a-zA-Z0-9_\-]/', '_', $message_data['subject']);
        $filename = 'mensagem_' . $message_id . '_' . $safe_subject . '.html';

        $content = '<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Histórico de Mensagem: ' . htmlspecialchars($message_data['subject']) . '</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .message-container { border: 1px solid #ddd; padding: 15px; margin-bottom: 20px; }
        .message-header { background-color: #f5f5f5; padding: 10px; margin-bottom: 10px; }
        .message-body { padding: 10px; }
        .reply { border-left: 4px solid #007bff; padding-left: 15px; margin: 15px 0; }
        .metadata { color: #666; font-size: 0.9em; margin-top: 5px; }
    </style>
</head>
<body>
    <h1>Histórico de Mensagem</h1>

    <div class="message-container">
        <div class="message-header">
            <h2>' . htmlspecialchars($message_data['subject']) . '</h2>
            <div class="metadata">
                <strong>De:</strong> ' . htmlspecialchars($message_data['name']) . ' (' . htmlspecialchars($message_data['email']) . ')<br>
                <strong>Data:</strong> ' . format_date($message_data['created_at']) . '<br>
                ' . (!empty($message_data['phone']) ? '<strong>Telefone:</strong> ' . htmlspecialchars($message_data['phone']) . '<br>' : '') . '
                ' . (!empty($message_data['product_ref']) ? '<strong>Referência do Produto:</strong> ' . htmlspecialchars($message_data['product_ref']) . '<br>' : '') . '
            </div>
        </div>
        <div class="message-body">
            ' . nl2br(htmlspecialchars($message_data['original_message'])) . '
        </div>
    </div>';

        if (!empty($replies)) {
            $content .= '<h2>Respostas</h2>';

            foreach ($replies as $reply) {
                $content .= '
    <div class="reply">
        <div class="metadata">
            <strong>Data:</strong> ' . format_date($reply['sent_at']) . '
        </div>
        <div class="message-body">
            ' . nl2br(htmlspecialchars($reply['reply_body'])) . '
        </div>
    </div>';
            }
        }

        $content .= '
</body>
</html>';

        while (ob_get_level()) {
            ob_end_clean();
        }

        header('Content-Type: text/html; charset=utf-8');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Content-Length: ' . strlen($content));
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');

        echo $content;
        exit;

    } catch (Exception $e) {
        add_flash_message('Erro ao gerar o download da mensagem: ' . $e->getMessage(), 'danger');
        header('Location: admin.php?section=messages&' . get_session_id_param());
        exit;
    }
}

elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'GET' && $section === 'products' && $action === 'get_product_info_fields') {
    header('Content-Type: application/json');
    $product_id = filter_input(INPUT_GET, 'product_id', FILTER_VALIDATE_INT);

    if (!$product_id) {
        echo json_encode(['success' => false, 'message' => 'ID do produto inválido']);
        exit;
    }

    require_once __DIR__ . '/includes/product_info_fields.php';
    $fields = get_product_info_fields($product_id);

    echo json_encode(['success' => true, 'fields' => $fields]);
    exit;
}

elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'GET' && $section === 'blog_categories' && $action === 'delete' && isset($_GET['id'])) {
    $delete_id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

    if ($delete_id) {
        if (delete_blog_category($delete_id)) {
            add_flash_message('Categoria de blog removida com sucesso.', 'success');
        } else {
            add_flash_message('Erro ao remover categoria de blog.', 'danger');
        }
    } else {
        add_flash_message('ID de categoria inválido para remoção.', 'warning');
    }
    header('Location: admin.php?section=blog_categories&' . get_session_id_param());
    exit;
}

elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'GET' && $section === 'licenses' && $action === 'delete' && isset($_GET['id'])) {

    $delete_id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
    $submitted_csrf = $_GET['csrf_token'] ?? '';

    if (!$delete_id) {
        add_flash_message('ID de licença inválido para remoção.', 'warning');
    } elseif (!validate_csrf_token($submitted_csrf)) {
        add_flash_message('Erro de segurança (CSRF). Tente novamente.', 'danger');
    } else {
        require_once __DIR__ . '/includes/digital_product_functions.php';
        $license = get_license_by_id($delete_id);
        if ($license) {
            if (delete_license($delete_id)) {
                add_flash_message('Licença excluída com sucesso.', 'success');
            } else {
                add_flash_message('Erro ao excluir licença.', 'danger');
            }
        } else {
            add_flash_message('Licença não encontrada.', 'danger');
        }
    }

    header('Location: admin.php?section=licenses&' . get_session_id_param());
    exit;
}

elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'GET' && $section === 'blog_posts' && $action === 'delete' && isset($_GET['id'])) {
    $delete_id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

    if ($delete_id) {

        $post_to_delete = get_blog_post($delete_id);
        $image_path_to_delete = $post_to_delete['image_path'] ?? null;

        if (delete_blog_post($delete_id)) {

            if ($image_path_to_delete && file_exists(__DIR__ . '/' . $image_path_to_delete)) {
                @unlink(__DIR__ . '/' . $image_path_to_delete);
            }
            add_flash_message('Post de blog removido com sucesso.', 'success');
        } else {
            add_flash_message('Erro ao remover post de blog.', 'danger');
        }
    } else {
        add_flash_message('ID de post inválido para remoção.', 'warning');
    }
    header('Location: admin.php?section=blog_posts&' . get_session_id_param());
    exit;
}

elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'GET' && $section === 'custom_fields' && $action === 'delete' && isset($_GET['id'])) {
    $delete_id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

    if ($delete_id) {

        $delete_result = delete_custom_field($delete_id);

        if ($delete_result) {
            add_flash_message('Campo personalizado removido com sucesso.', 'success');
        } else {
            add_flash_message('Erro ao remover campo personalizado.', 'danger');
        }
    } else {
        add_flash_message('ID de campo personalizado inválido para remoção.', 'warning');
    }

    while (ob_get_level()) {
        ob_end_clean();
    }

    if (headers_sent($file, $line)) {

        echo "<script>window.location.href='admin.php?section=custom_fields&" . get_session_id_param() . "';</script>";
    } else {

        header('Location: admin.php?section=custom_fields&' . get_session_id_param());
    }
    exit;
}

elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'GET' && $section === 'custom_fields' && $action === 'duplicate' && isset($_GET['id'])) {
    $duplicate_id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

    if ($duplicate_id) {

        $new_field_id = duplicate_custom_field($duplicate_id);

        if ($new_field_id) {
            add_flash_message('Campo personalizado duplicado com sucesso.', 'success');

            header('Location: admin.php?section=custom_fields&action=edit&id=' . $new_field_id . '&' . get_session_id_param());
            exit;
        } else {
            add_flash_message('Erro ao duplicar campo personalizado.', 'danger');
        }
    } else {
        add_flash_message('ID de campo personalizado inválido para duplicação.', 'warning');
    }

    header('Location: admin.php?section=custom_fields&' . get_session_id_param());
    exit;
}

elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'GET' && $section === 'download_statistics' && $action === 'delete' && isset($_GET['id'])) {
    $download_id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);

    $submitted_csrf = $_GET['csrf_token'] ?? '';
    if (!validate_csrf_token($submitted_csrf)) {
        add_flash_message('Erro de segurança (CSRF). Tente novamente.', 'danger');
        header('Location: admin.php?section=download_statistics&' . get_session_id_param());
        exit;
    }

    if ($download_id) {
        require_once __DIR__ . '/includes/digital_product_functions.php';

        if (delete_download_record($download_id)) {
            add_flash_message('Registro de download removido com sucesso.', 'success');
        } else {
            add_flash_message('Erro ao remover registro de download.', 'danger');
        }
    } else {
        add_flash_message('ID de download inválido para remoção.', 'warning');
    }

    $redirect_url = 'admin.php?section=download_statistics';

    if (isset($_GET['product_id'])) $redirect_url .= '&product_id=' . urlencode($_GET['product_id']);
    if (isset($_GET['customer_email'])) $redirect_url .= '&customer_email=' . urlencode($_GET['customer_email']);
    if (isset($_GET['date_from'])) $redirect_url .= '&date_from=' . urlencode($_GET['date_from']);
    if (isset($_GET['date_to'])) $redirect_url .= '&date_to=' . urlencode($_GET['date_to']);
    if (isset($_GET['p'])) $redirect_url .= '&p=' . urlencode($_GET['p']);

    $redirect_url .= '&' . get_session_id_param();

    header('Location: ' . $redirect_url);
    exit;
}

elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'POST' && $section === 'download_statistics' && $action === 'delete_multiple') {

    $submitted_csrf = $_POST['csrf_token'] ?? '';
    if (!validate_csrf_token($submitted_csrf)) {
        add_flash_message('Erro de segurança (CSRF). Tente novamente.', 'danger');
        header('Location: admin.php?section=download_statistics&' . get_session_id_param());
        exit;
    }

    require_once __DIR__ . '/includes/digital_product_functions.php';

    $download_ids = isset($_POST['download_ids']) ? $_POST['download_ids'] : [];

    if (!empty($download_ids)) {

        $download_ids = array_map('intval', $download_ids);

        $download_ids = array_filter($download_ids, function($id) {
            return $id > 0;
        });

        if (!empty($download_ids)) {
            $result = delete_multiple_download_records($download_ids);

            if ($result['success'] > 0) {
                $message = $result['success'] . ' registro(s) de download removido(s) com sucesso.';
                if ($result['error'] > 0) {
                    $message .= ' ' . $result['error'] . ' registro(s) não puderam ser removidos.';
                }
                add_flash_message($message, 'success');
            } else {
                add_flash_message('Erro ao remover registros de download.', 'danger');
            }
        } else {
            add_flash_message('Nenhum registro válido selecionado para remoção.', 'warning');
        }
    } else {
        add_flash_message('Nenhum registro selecionado para remoção.', 'warning');
    }

    $redirect_url = 'admin.php?section=download_statistics';

    if (isset($_POST['product_id'])) $redirect_url .= '&product_id=' . urlencode($_POST['product_id']);
    if (isset($_POST['customer_email'])) $redirect_url .= '&customer_email=' . urlencode($_POST['customer_email']);
    if (isset($_POST['date_from'])) $redirect_url .= '&date_from=' . urlencode($_POST['date_from']);
    if (isset($_POST['date_to'])) $redirect_url .= '&date_to=' . urlencode($_POST['date_to']);
    if (isset($_POST['p'])) $redirect_url .= '&p=' . urlencode($_POST['p']);

    $redirect_url .= '&' . get_session_id_param();

    header('Location: ' . $redirect_url);
    exit;
}

elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'GET' && $section === 'products' && $action === 'add_product_info_field') {
    header('Content-Type: application/json');
    $product_id = filter_input(INPUT_GET, 'product_id', FILTER_VALIDATE_INT);
    $icon = trim($_GET['icon'] ?? '');
    $text = trim($_GET['text'] ?? '');

    if (!$product_id) {
        echo json_encode(['success' => false, 'message' => 'ID do produto inválido']);
        exit;
    }

    if (empty($icon) && empty($text)) {
        echo json_encode(['success' => false, 'message' => 'Campos vazios não são permitidos']);
        exit;
    }

    require_once __DIR__ . '/includes/product_info_fields.php';

    $existing_field = db_query(
        "SELECT id FROM product_info_fields WHERE product_id = :pid AND icon = :icon AND text = :text LIMIT 1",
        [':pid' => $product_id, ':icon' => $icon, ':text' => $text],
        true
    );

    if ($existing_field && isset($existing_field['id'])) {
        echo json_encode(['success' => true, 'field_id' => $existing_field['id'], 'icon' => $icon, 'text' => $text, 'message' => 'Campo existente encontrado']);
        exit;
    }

    $field_id = add_product_info_field($product_id, $icon, $text);

    if ($field_id) {
        echo json_encode(['success' => true, 'field_id' => $field_id, 'icon' => $icon, 'text' => $text]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Falha ao adicionar campo']);
    }
    exit;
}

elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'GET' && $section === 'products' && $action === 'delete_product_info_field') {
    header('Content-Type: application/json');
    $field_id = filter_input(INPUT_GET, 'field_id', FILTER_VALIDATE_INT);

    if (!$field_id) {
        echo json_encode(['success' => false, 'message' => 'ID do campo inválido']);
        exit;
    }

    require_once __DIR__ . '/includes/product_info_fields.php';
    $result = delete_product_info_field($field_id);

    echo json_encode(['success' => $result]);
    exit;
}

elseif ($is_admin_logged_in && $_SERVER['REQUEST_METHOD'] === 'GET' && $section === 'products' && $action === 'update_product_info_field') {
    header('Content-Type: application/json');
    $field_id = filter_input(INPUT_GET, 'field_id', FILTER_VALIDATE_INT);
    $icon = trim($_GET['icon'] ?? '');
    $text = trim($_GET['text'] ?? '');

    if (!$field_id) {
        echo json_encode(['success' => false, 'message' => 'ID do campo inválido']);
        exit;
    }

    require_once __DIR__ . '/includes/product_info_fields.php';
    $result = update_product_info_field($field_id, $icon, $text);

    echo json_encode(['success' => $result, 'field_id' => $field_id, 'icon' => $icon, 'text' => $text]);
    exit;
}

$admin_view_data = prepare_admin_view_data($section, $action, $item_id, $settings, $current_session_id, $is_admin_logged_in, $admin_page_title);

if (isset($_SESSION['redirect_to'])) {
    $redirect_url = $_SESSION['redirect_to'];
    unset($_SESSION['redirect_to']);
    header('Location: ' . $redirect_url);
    exit;
}

if (isset($admin_view_data['redirect_to'])) {
    header('Location: ' . $admin_view_data['redirect_to']);
    exit;
}

if ($is_admin_logged_in) {

    if ($section === 'products' && $action === 'edit' && $item_id && isset($admin_view_data['variations'])) {
        foreach ($admin_view_data['variations'] as &$variation) {
            $variation['values'] = db_query(
                "SELECT vv.value_id, av.value_pt, av.attribute_id, a.name_pt as attribute_name
                 FROM variation_values vv
                 JOIN attribute_values av ON vv.value_id = av.id
                 JOIN attributes a ON av.attribute_id = a.id
                 WHERE vv.variation_id = :vid",
                [':vid' => $variation['id']],
                false, true
            ) ?: [];
        }
        unset($variation);
    }

    if ($section === 'attributes' && $action === 'values' && isset($_GET['attribute_id'])) {
        $admin_view_data['attribute_id'] = (int)$_GET['attribute_id'];
        $attr_info = db_query("SELECT name_pt FROM attributes WHERE id = :id", [':id' => $admin_view_data['attribute_id']], true);
        if ($attr_info) {
            $admin_view_data['attribute_name'] = $attr_info['name_pt'];
        }
    }

    if (isset($_SESSION['form_data'])) {
        $admin_view_data['form_data'] = $_SESSION['form_data'];
        unset($_SESSION['form_data']);
    }
}

$csrf_token = '';
if ($is_admin_logged_in) {

    $csrf_token = generate_csrf_token();
    $admin_view_data['csrf_token'] = $csrf_token;

    $admin_view_data['csrf_token_for_js'] = $csrf_token;
}

include_template('backend/partials/header.php', $admin_view_data);

if (!$is_admin_logged_in) {

    echo '<style>.admin-content { width: 100% !important; margin-left: 0 !important; }</style>';
    echo '<div class="container py-5">';
    $admin_view_data['login_error'] = $login_error;
    include_template('backend/login.php', $admin_view_data);
    echo '</div>';
} else {

    echo '<main class="p-4">';
    switch ($section) {
        case 'products':
            $admin_view_data['page_title'] = "Gerir Produtos - " . $admin_page_title;
            if ($action === 'edit' || $action === 'new') {

                include_template('backend/product_form.php', $admin_view_data);
            } elseif ($action === 'images') {
                $admin_view_data['page_title'] = "Gerir Imagens de Produtos - " . $admin_page_title;
                
                
                $product_name_filter = isset($_GET['product_name']) ? trim($_GET['product_name']) : '';
                $format_filter = isset($_GET['format']) ? $_GET['format'] : '';
                $sort_option = isset($_GET['sort']) ? $_GET['sort'] : 'product_name';
                
                
                $images_per_page = 20;
                $current_page = isset($_GET['p']) ? max(1, (int)$_GET['p']) : 1;
                $offset = ($current_page - 1) * $images_per_page;
                
                
                $images_result = get_product_images_filtered($product_name_filter, $format_filter, $sort_option, $images_per_page, $offset);
                $total_images = get_product_images_count($product_name_filter, $format_filter);
                $total_pages = ceil($total_images / $images_per_page);
                
                $admin_view_data['product_images_categorized'] = $images_result;
                $admin_view_data['product_name_filter'] = $product_name_filter;
                $admin_view_data['format_filter'] = $format_filter;
                $admin_view_data['sort_option'] = $sort_option;
                $admin_view_data['current_page'] = $current_page;
                $admin_view_data['total_pages'] = $total_pages;
                $admin_view_data['total_images'] = $total_images;
                
                include_template('backend/products_images.php', $admin_view_data);
            } else {

                include_template('backend/products_list.php', $admin_view_data);
            }
            break;

        case 'attributes':
             $admin_view_data['page_title'] = "Gerir Atributos - " . $admin_page_title;
             if ($action === 'edit' || $action === 'new') {

                 include_template('backend/attribute_form.php', $admin_view_data);
             } elseif ($action === 'values' && isset($_GET['attribute_id'])) {
                 
                 $action_type = $_GET['action_type'] ?? '';
                 if ($action_type === 'add' || $action_type === 'edit') {
                     include_template('backend/attribute_value_form.php', $admin_view_data);
                 } else {
                     include_template('backend/attribute_values_list.php', $admin_view_data);
                 }
             } else {

                 include_template('backend/attributes_list.php', $admin_view_data);
             }
             break;

        case 'orders':
             $admin_view_data['page_title'] = "Gerir Encomendas - " . $admin_page_title;
             if ($action === 'detail') {

                 include_template('backend/order_detail.php', $admin_view_data);
             } else {

                 include_template('backend/orders_list.php', $admin_view_data);
             }
            break;
        case 'pages':
             $admin_view_data['page_title'] = "Gerir Páginas - " . $admin_page_title;
             if ($action === 'edit' || $action === 'new') {

                 include_template('backend/page_form.php', $admin_view_data);
             } else {

                 include_template('backend/pages_list.php', $admin_view_data);
             }
            break;
        case 'page_categories':
             $admin_view_data['page_title'] = "Categorias de Páginas - " . $admin_page_title;
             if ($action === 'edit' || $action === 'new') {

                 include_template('backend/page_category_form.php', $admin_view_data);
             } else {

                 include_template('backend/page_categories_list.php', $admin_view_data);
             }
            break;

        case 'page_placeholders':
             $admin_view_data['page_title'] = "Placeholders de Páginas - " . $admin_page_title;
             if ($action === 'edit' || $action === 'new') {

                 include_template('backend/page_placeholder_form.php', $admin_view_data);
             } else {

                 include_template('backend/page_placeholders_list.php', $admin_view_data);
             }
            break;

        case 'placeholder_links':
             $admin_view_data['page_title'] = "Links de Placeholders - " . $admin_page_title;
             if ($action === 'edit' || $action === 'new') {

                 include_template('backend/placeholder_link_form.php', $admin_view_data);
             } else {

                 include_template('backend/placeholder_links_list.php', $admin_view_data);
             }
            break;

       case 'categories':
            $admin_view_data['page_title'] = "Categorias de Produtos - " . $admin_page_title;
            if ($action === 'edit' || $action === 'new') {

                include_template('backend/category_form.php', $admin_view_data);
            } else {

                include_template('backend/categories_list.php', $admin_view_data);
            }
           break;

       case 'settings':
            $admin_view_data['page_title'] = "Configurações - " . $admin_page_title;
             include_template('backend/settings.php', $admin_view_data);
            break;
        case 'messages':
             $admin_page_title = "Mensagens - " . $admin_page_title;

             include_template('backend/messages.php', $admin_view_data);
            break;
        case 'coupons':
            $admin_view_data['page_title'] = "Gerir Cupões - " . $admin_page_title;
            if ($action === 'edit' || $action === 'new') {

                include_template('backend/coupon_form.php', $admin_view_data);
            } else {

                include_template('backend/coupons_list.php', $admin_view_data);
            }
            break;

        case 'licenses':
            $admin_view_data['page_title'] = "Gerir Licenças - " . $admin_page_title;

            if ($action === 'generate_code' && ($is_ajax_request || isset($_GET['is_ajax']))) {
                try {
                    require_once __DIR__ . '/includes/digital_product_functions.php';
                    $license_code = generate_license_code();

                    $exists = db_query(
                        "SELECT 1 FROM licenses WHERE license_code = :code",
                        [':code' => $license_code],
                        true
                    );

                    while ($exists) {
                        $license_code = generate_license_code();
                        $exists = db_query(
                            "SELECT 1 FROM licenses WHERE license_code = :code",
                            [':code' => $license_code],
                            true
                        );
                    }

                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => true,
                        'license_code' => $license_code
                    ]);
                } catch (Exception $e) {
                    header('Content-Type: application/json');
                    echo json_encode([
                        'success' => false,
                        'message' => 'Erro ao gerar código de licença: ' . $e->getMessage()
                    ]);
                }
                exit;
            }

            if ($action === 'reset_downloads' && $item_id) {
                require_once __DIR__ . '/includes/digital_product_functions.php';
                if (reset_license_downloads($item_id)) {
                    add_flash_message('Contagem de downloads redefinida com sucesso.', 'success');
                } else {
                    add_flash_message('Erro ao redefinir contagem de downloads.', 'danger');
                }

                $redirect_url = isset($_GET['return_to']) && $_GET['return_to'] === 'detail'
                    ? "admin.php?section=licenses&action=detail&id={$item_id}"
                    : "admin.php?section=licenses";

                header('Location: ' . $redirect_url . '&' . get_session_id_param());
                exit;
            }

            else if ($action === 'delete_download_history' && $item_id) {
                require_once __DIR__ . '/includes/digital_product_functions.php';
                $download_id = isset($_GET['download_id']) ? (int)$_GET['download_id'] : null;

                if (delete_license_download_history($item_id, $download_id)) {
                    if ($download_id) {
                        add_flash_message('Registro de download excluído com sucesso.', 'success');
                    } else {
                        add_flash_message('Histórico de downloads excluído com sucesso.', 'success');
                    }
                } else {
                    add_flash_message('Erro ao excluir histórico de downloads.', 'danger');
                }

                header('Location: admin.php?section=licenses&action=detail&id=' . $item_id . '&' . get_session_id_param());
                exit;
            }

            else if ($action === 'toggle_status' && $item_id) {
                require_once __DIR__ . '/includes/digital_product_functions.php';
                $new_status = $_GET['status'] ?? null;

                if (toggle_license_status($item_id, $new_status)) {
                    add_flash_message('Status da licença atualizado com sucesso.', 'success');
                    if ($new_status === 'active') {
                        
                        $_SESSION['prompt_send_license_email'] = $item_id;
                    }
                } else {
                    add_flash_message('Erro ao atualizar status da licença.', 'danger');
                }

                $redirect_url = isset($_GET['return_to']) && $_GET['return_to'] === 'detail'
                    ? "admin.php?section=licenses&action=detail&id={$item_id}"
                    : "admin.php?section=licenses";

                header('Location: ' . $redirect_url . '&' . get_session_id_param());
                exit;
            }

            else if ($action === 'update_expiry' && $item_id && isset($_POST['expiry_date'])) {
                require_once __DIR__ . '/includes/digital_product_functions.php';
                $expiry_date = sanitize_input($_POST['expiry_date'] ?? '');

                if (empty($expiry_date)) {
                    add_flash_message('Data de expiração inválida.', 'danger');
                } else if (update_license_expiry_date($item_id, $expiry_date)) {
                    add_flash_message('Data de expiração atualizada com sucesso.', 'success');
                } else {
                    add_flash_message('Erro ao atualizar data de expiração.', 'danger');
                }

                $redirect_url = isset($_GET['return_to']) && $_GET['return_to'] === 'detail'
                    ? "admin.php?section=licenses&action=detail&id={$item_id}"
                    : "admin.php?section=licenses";

                header('Location: ' . $redirect_url . '&' . get_session_id_param());
                exit;
            }

            else if ($action === 'send_direct_download_link' && $item_id) {
                
                if (!validate_csrf_token($_GET['csrf_token'] ?? '')) {
                    add_flash_message('Erro de segurança (CSRF). Tente novamente.', 'danger');
                } else {
                    require_once __DIR__ . '/includes/digital_product_functions.php';
                    $license = get_license_by_id($item_id);
                    if ($license) {
                        if (send_license_download_email($license)) {
                            add_flash_message('Link de download enviado para o cliente com sucesso.', 'success');
                        } else {
                            add_flash_message('Erro ao enviar o link de download para o cliente.', 'danger');
                        }
                    } else {
                        add_flash_message('Licença não encontrada.', 'danger');
                    }
                }
                
                
                $redirect_url = "admin.php?section=licenses&action=detail&id={$item_id}";
                header('Location: ' . $redirect_url . '&' . get_session_id_param());
                exit;
            }

            else if ($action === 'edit' || $action === 'new') {
                include_template('backend/license_form.php', $admin_view_data);
            } else if ($action === 'detail') {
                include_template('backend/license_detail.php', $admin_view_data);
            } else {
                include_template('backend/licenses_list.php', $admin_view_data);
            }
            break;

        case 'payment_methods':
            $admin_view_data['page_title'] = "Gerir Métodos de Pagamento - " . $admin_page_title;

            $table_ready = ensure_payment_methods_table_exists();

            if ($action === 'edit' && $item_id) {

                $admin_view_data['payment_method'] = get_payment_method($item_id);
                if (!$admin_view_data['payment_method']) {
                    add_flash_message("Método de pagamento não encontrado.", 'danger');
                    $_SESSION['redirect_to'] = 'admin.php?section=payment_methods&' . get_session_id_param();
                    return;
                }
                include_template('backend/payment_method_form.php', $admin_view_data);
            } elseif ($action === 'new') {
                include_template('backend/payment_method_form.php', $admin_view_data);
            } elseif ($action === 'delete' && $item_id) {
                if (delete_payment_method($item_id)) {
                    add_flash_message('Método de pagamento removido com sucesso.', 'success');
                } else {
                    add_flash_message('Erro ao remover método de pagamento.', 'danger');
                }
                $_SESSION['redirect_to'] = 'admin.php?section=payment_methods&' . get_session_id_param();
                return;

            } else {

                $admin_view_data['payment_methods'] = get_payment_methods();

                if (empty($admin_view_data['payment_methods'])) {

                    ensure_payment_methods_table_exists();
                    $admin_view_data['payment_methods'] = get_payment_methods();
                }

                include_template('backend/payment_methods_list.php', $admin_view_data);
            }
            break;

        case 'blog_categories':
            $admin_view_data['page_title'] = "Categorias do Blog - " . $admin_page_title;
            if ($action === 'edit' || $action === 'new') {

                include_template('backend/blog_category_form.php', $admin_view_data);
            } else {

                include_template('backend/blog_categories_list.php', $admin_view_data);
            }
            break;

        case 'blog_posts':
            $admin_view_data['page_title'] = "Posts do Blog - " . $admin_page_title;
            if ($action === 'edit' || $action === 'new') {

                include_template('backend/blog_post_form.php', $admin_view_data);
            } else {

                include_template('backend/blog_posts_list.php', $admin_view_data);
            }
            break;

        case 'custom_fields':
             $admin_view_data['page_title'] = "Campos Personalizados - " . $admin_page_title;
             if ($action === 'edit' && $item_id) {

                 include_template('backend/custom_field_form.php', $admin_view_data);
             } elseif ($action === 'new') {

                 include_template('backend/custom_field_form.php', $admin_view_data);
             } else {

                 include_template('backend/custom_fields_list.php', $admin_view_data);
             }
             break;

        case 'custom_field_fonts':
            $admin_view_data['page_title'] = "Fontes para Campos Personalizados - " . $admin_page_title;
            if ($action === 'edit' && $item_id) {

                $admin_view_data['font'] = get_custom_field_font($item_id);
                if (!$admin_view_data['font']) {
                    add_flash_message("Fonte não encontrada.", 'danger');
                    $_SESSION['redirect_to'] = 'admin.php?section=custom_field_fonts&' . get_session_id_param();
                    return;
                }
                include_template('backend/custom_field_font_form.php', $admin_view_data);
            } elseif ($action === 'new') {
                include_template('backend/custom_field_font_form.php', $admin_view_data);
            } elseif ($action === 'delete' && $item_id) {
                if (delete_custom_field_font($item_id)) {
                    add_flash_message('Fonte removida com sucesso.', 'success');
                } else {
                    add_flash_message('Erro ao remover fonte.', 'danger');
                }
                $_SESSION['redirect_to'] = 'admin.php?section=custom_field_fonts&' . get_session_id_param();
                return;
            } else {

                include_template('backend/custom_field_fonts_list.php', $admin_view_data);
            }
            break;

        case 'digital_products':
            require_once __DIR__ . '/includes/digital_product_functions.php';
            require_once __DIR__ . '/includes/digital_files_functions.php';
            
            $admin_view_data['page_title'] = "Produtos Digitais - " . $admin_page_title;
            
            if ($action === 'edit' && $item_id) {
                $admin_view_data['product_data'] = get_product_by_id($item_id);
                if (!$admin_view_data['product_data']) {
                    add_flash_message("Produto não encontrado.", 'danger');
                    $_SESSION['redirect_to'] = 'admin.php?section=products&' . get_session_id_param();
                    return;
                }
                
                $admin_view_data['digital_product'] = get_digital_product_by_product_id($item_id);
                $admin_view_data['all_file_types'] = get_all_file_types();
                
                if ($admin_view_data['digital_product']) {
                    $admin_view_data['selected_file_types'] = get_digital_product_file_type_ids($admin_view_data['digital_product']['id']);
                }
                
                include_template('backend/digital_product_form.php', $admin_view_data);
            } elseif ($action === 'new' && $item_id) {
                $admin_view_data['product_data'] = get_product_by_id($item_id);
                if (!$admin_view_data['product_data']) {
                    add_flash_message("Produto não encontrado.", 'danger');
                    $_SESSION['redirect_to'] = 'admin.php?section=products&' . get_session_id_param();
                    return;
                }
                
                $admin_view_data['digital_product'] = null;
                $admin_view_data['all_file_types'] = get_all_file_types();
                
                include_template('backend/digital_product_form.php', $admin_view_data);
            } else {
                include_template('backend/digital_products_list.php', $admin_view_data);
            }
            break;

        case 'digital_files':
            require_once __DIR__ . '/includes/digital_files_functions.php';
            require_once __DIR__ . '/includes/digital_product_functions.php';

            $admin_view_data['page_title'] = "Gestão de Arquivos Digitais - " . $admin_page_title;

            $digital_product_id = isset($_GET['id']) ? (int)$_GET['id'] : (isset($_GET['item_id']) ? (int)$_GET['item_id'] : 0);

            if ($action === 'change_file' && $digital_product_id > 0) {
                
                $admin_view_data['item_id'] = $digital_product_id;
                include_template('backend/change_digital_file.php', $admin_view_data);
            } elseif ($action === 'list' || empty($action)) {
                
                include_template('backend/digital_files_list.php', $admin_view_data);
            } elseif ($action === 'manage') {
                
                include_template('backend/digital_files_management.php', $admin_view_data);
            } else {
                
                include_template('backend/digital_files_list.php', $admin_view_data);
            }
            break;

        case 'sitemaps':
            $admin_view_data['page_title'] = "Sitemaps e XMLs - " . $admin_page_title;
            
            require_once __DIR__ . '/includes/sitemap_functions.php';
            ensure_sitemap_configs_table_exists();
            
            if ($action === 'edit' && $item_id) {
                $sitemap = get_sitemap_config($item_id);
                if ($sitemap) {
                    $admin_view_data['sitemap'] = $sitemap;
                    include_template('backend/sitemap_form.php', $admin_view_data);
                } else {
                    add_flash_message('Configuração de sitemap não encontrada.', 'danger');
                    include_template('backend/sitemaps_list.php', $admin_view_data);
                }
            } elseif ($action === 'new') {
                include_template('backend/sitemap_form.php', $admin_view_data);
            } else {
                include_template('backend/sitemaps_list.php', $admin_view_data);
            }
            break;

        case 'sessions':
            $admin_view_data['page_title'] = "Gerir Sessões - " . $admin_page_title;



            include_template('backend/sessions_list.php', $admin_view_data);
            break;

        case 'recommended_products':
            // Include the recommended products functions
            require_once __DIR__ . '/includes/recommended_products_functions.php';

            $admin_view_data['page_title'] = "Gerir Produtos Recomendados - " . $admin_page_title;

            // Get current recommended products
            $admin_view_data['recommended_products'] = get_recommended_products(true);
            $admin_view_data['recommended_count'] = get_recommended_products_count();

            // For adding new products - get search term if provided
            $search_term = $_GET['search'] ?? '';
            $admin_view_data['search_term'] = $search_term;
            $admin_view_data['available_products'] = get_products_available_for_recommendation($search_term, 20);

            include_template('backend/recommended_products_list.php', $admin_view_data);
            break;

        case 'banners':
            $admin_view_data['page_title'] = "Gerenciar Banners - " . $admin_page_title;
            include_template('backend/banners.php', $admin_view_data);
            break;

        case 'maintenance':
            $admin_view_data['page_title'] = "Manutenção - " . $admin_page_title;
            include_template('backend/maintenance_simple.php', $admin_view_data);
            break;

        case 'dashboard':
        default:
            $admin_view_data['page_title'] = "Dashboard - " . $admin_page_title;
            include_template('backend/dashboard.php', $admin_view_data);
            break;
    }

    echo '</main>';
}

echo '</div>';

echo '</div>';

include_template('backend/partials/footer.php', $admin_view_data);

?>
